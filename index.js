/**
 * @format
 */
import React from 'react';
import {AppRegistry, StatusBar} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import { ModalPortal } from 'react-native-modals';
import { AlertNotificationRoot } from 'react-native-alert-notification';


function BaseApp () {
  return (
    <React.Fragment>
      <StatusBar translucent={true} hidden={true} animated={true}/>
      <AlertNotificationRoot>
        <App />
      </AlertNotificationRoot>
      {/* <App /> */}
      <ModalPortal />
    </React.Fragment>
  )
}

AppRegistry.registerComponent(appName, () => BaseApp);
