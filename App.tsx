/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React from 'react';
import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import type { PropsWithChildren } from 'react';

// 定时器管理器
class TimerManager {
  private timers: Map<string, NodeJS.Timeout> = new Map();

  setTimer(key: string, callback: () => void, delay: number): void {
    this.clearTimer(key);
    const timer = setInterval(callback, delay);
    this.timers.set(key, timer);
  }

  setTimeout(key: string, callback: () => void, delay: number): void {
    this.clearTimer(key);
    const timer = setTimeout(callback, delay);
    this.timers.set(key, timer);
  }

  clearTimer(key: string): void {
    const timer = this.timers.get(key);
    if (timer) {
      clearInterval(timer);
      clearTimeout(timer);
      this.timers.delete(key);
    }
  }

  clearAllTimers(): void {
    this.timers.forEach((timer) => {
      clearInterval(timer);
      clearTimeout(timer);
    });
    this.timers.clear();
  }

  hasTimer(key: string): boolean {
    return this.timers.has(key);
  }
}
import {
  SafeAreaView,
  StyleSheet,
  Text,
  useColorScheme,
  View,
  Platform,
  Dimensions,
  Image,
  TextInput,
  Button,
  ImageBackground,
  Alert,
  FlatList,
  TouchableOpacity
} from 'react-native';
import NetInfo from '@react-native-community/netinfo'
import { CameraRoll } from "@react-native-camera-roll/camera-roll";

import {
  Colors
} from 'react-native/Libraries/NewAppScreen';

import Swiper from 'react-native-swiper'
import Video, { ResizeMode } from 'react-native-video'

import RNFS from 'react-native-fs';
import { getListStation, getCurrentInfo, getAppState, setCarStatus } from './util/common'
import { AMapSdk, MapView, MapType, Marker } from "react-native-amap3d";
import Icon from 'react-native-vector-icons/Ionicons'
import AsyncStorage from '@react-native-async-storage/async-storage';
import RouterLine from './components/routerline';
import Dialog from './components/dialog';
import { Buffer } from 'buffer';
import TcpSocket from 'react-native-tcp-socket';
import { CountdownCircleTimer } from 'react-native-countdown-circle-timer'

import { HttpBaseGet, wgs84togcj02 } from './util/base';
import { ApiResponse, Station, MediaItem, Point, Marker as MarkerType, SocketCallBackData, SocketOptions, DebugCoordinates } from './types';
// import DebugPanel from './components/DebugPanel';

let mapView: MapView;

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height + 20;
const baseWidth = 1080

// 自适应字体函数
const scaleSize = (size: number) => {
  // 计算缩放比例
  const scale = windowWidth / baseWidth;
  // 返回缩放后的字体大小
  return size * scale;
};

type SectionProps = PropsWithChildren<{
  title: string;
}>;


type Markers = {
  title: string,
  lat: string,
  lon: string,
  pic: string,
}

type socketOptions = {
  host: string,
  port: number,
}

const storeData = async (key: string, value: string) => {
  try {
    const _value = await AsyncStorage.getItem(key);
    if (_value !== value) {
      await AsyncStorage.setItem(key, value);
    }
  } catch (e) {
    // saving error
    console.log(e);
  }
};

const initData = async (key: string, callback: (val: string) => void) => {
  try {
    const value = await AsyncStorage.getItem(key);
    if (value) {
      callback(value);
    }
  } catch (e) {
    // saving error
    console.log(e);
  }
};

let Socket: TcpSocket.Socket | undefined;

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  // 创建定时器管理器实例
  const timerManager = useMemo(() => new TimerManager(), []);

  const [position, setPosition] = useState<{ latitude: number, longitude: number }>({
    latitude: 30.89462,
    longitude: 121.906606,
  });

  const [carPosition, setCarPosition] = useState<{ carLng: number, carLat: number }>({
    carLng: 121.906606,
    carLat: 30.89462
  });

  const [speed, setSpeed] = useState<string>("0");

  const [spatAlarmType, setSpatAlarmType] = useState<number>(0);

  const [pedestrianWarning, setPedestrianWarning] = useState<boolean>(false);
  const [junctionWarning, setJunctionWarning] = useState<boolean>(false);

  const [vslInformation, setVslInformation] = useState<{ overspeedStatus: number, advisorySpeed: number }>({
    overspeedStatus: 0, // 超速状态(0:无预警，1：预警)
    advisorySpeed: 0 //可变限速值 (km/h)
  });

  const [dialogVisible, setDialogVisible] = useState<boolean>(false);

  const [carID_form, setCarIdForm] = useState<string>('');
  const [ip_form, setIpForm] = useState('*************');
  const [port_form, setProtForm] = useState('7878');
  // const [ip_form, setIpForm] = useState<string>('**************');
  // const [port_form, setProtForm] = useState<string>('8888');
  const [remainingTime, setRemainingTime] = useState<number>(0);
  const [remainingTimeRgb, setRemainingTimeRgb] = useState<`#${string}`>('#008000');
  const [isPlaying, setIsPlaying] = useState<boolean>(false);

  const [showCoverApp, setShowCoverApp] = useState<boolean>(false)

  // Debug相关状态
  // const [debugVisible, setDebugVisible] = useState<boolean>(false);
  // const [debugData, setDebugData] = useState<DebugCoordinates | null>(null);
  // const [rawSocketData, setRawSocketData] = useState<any>(null);
  const [coverAppImg, setCoverAppImg] = useState<string | null>(null)

  const [netState, setNetState] = useState<boolean>(false)
  const [prevNetState, setPrevNetState] = useState<boolean>(false)

  const [markers, setMarkers] = useState<Markers[]>([]);

  // Debug相关状态
  const [debugMode, setDebugMode] = useState<boolean>(false);
  const [debugCoordinates, setDebugCoordinates] = useState<{
    latitude: number | null;
    longitude: number | null;
    timestamp: string;
    raw: any;
  }>({
    latitude: null,
    longitude: null,
    timestamp: '',
    raw: null
  });

  const setFrom = useCallback(() => {
    storeData('carID', carID_form);
    storeData('ip', ip_form);
    storeData('port', port_form);
    setCarIdForm(carID_form);
    setIpForm(ip_form);
    setProtForm(port_form);
    // Socket?.destroy();
    // Socket = initSocket({
    //   host: ip_form,
    //   port: Number(port_form)
    // });

    // 使用定时器管理器
    timerManager.clearTimer('listStation');
    timerManager.setTimeout('listStation', () => {
      handleListStation(carID_form)
    }, 10000);

    setDialogVisible(false);
  }, [carID_form, ip_form, port_form, timerManager]);

  let vslInformationTimer: NodeJS.Timeout | undefined;
  let spatAlarmTypeTimer: NodeJS.Timeout | undefined;
  let pedestrianWarningTimer: NodeJS.Timeout | undefined;
  let junctionWarningTimer: NodeJS.Timeout | undefined;


  let setTimeoutId: any;
  let _socketPlayTime: number = 0;

  // 创建全局数据缓冲区，保持连接间的数据状态
  let globalDataBuffer = Buffer.alloc(0);
  let isDataReceived = false;

  // 优化socket创建函数，使用定时器管理器和防抖
  const startSocket = useCallback((options: any, callBack: any) => {
    console.log('尝试连接');
    if (!options.port || !options.host) return;
    options.reuseAddress = true;

    // 使用防抖优化回调
    let lastCallbackTime = 0;
    let latestData: any = null;

    let client = TcpSocket.connect(options, function () {
      console.log('已连接到服务器');
    });

    client.on('data', function (data) {
      try {
        // 检查是否需要节流
        const currentTime = Date.now();
        if (isDataReceived && (currentTime - lastCallbackTime < 1000)) {
          // console.log('小于一秒，丢弃')
          // 如果距离上次成功处理不足1秒，则暂不处理
          return;
        }

        // 记录原始数据
        const _buf = Buffer.from(data);
        console.log('接收到数据:', data.toString());

        // 将新数据附加到全局缓冲区
        globalDataBuffer = Buffer.concat([globalDataBuffer, _buf]);

        // 处理缓冲区中的数据，可能包含多个完整的数据包
        let processed = false;

        while (globalDataBuffer.length > 0) {
          // 尝试在数据中查找JSON
          // 查找JSON开始的位置('{')和结束的位置('}')
          let jsonStart = -1;
          let jsonEnd = -1;
          let braceCount = 0;

          // 查找第一个 '{'
          for (let i = 0; i < globalDataBuffer.length; i++) {
            if (globalDataBuffer[i] === 123) { // '{' 的ASCII码是123
              jsonStart = i;
              break;
            }
          }

          if (jsonStart === -1) {
            // 没有找到JSON开始标记，清空缓冲区
            globalDataBuffer = Buffer.alloc(0);
            break;
          }

          // 从JSON开始位置查找匹配的结束位置
          for (let i = jsonStart; i < globalDataBuffer.length; i++) {
            if (globalDataBuffer[i] === 123) { // '{'
              braceCount++;
            } else if (globalDataBuffer[i] === 125) { // '}'
              braceCount--;
              if (braceCount === 0) {
                jsonEnd = i;
                break;
              }
            }
          }

          if (jsonEnd === -1 || braceCount !== 0) {
            // JSON不完整，等待更多数据
            break;
          }

          // 提取JSON部分
          const jsonData = globalDataBuffer.slice(jsonStart, jsonEnd + 1);

          try {
            // 解析JSON
            const jsonString = jsonData.toString();
            const _JSON = JSON.parse(jsonString);

            // console.log('解析成功:',
            //   'latitude: ', _JSON.position_3d?.latitude,
            //   'longitude: ', _JSON.position_3d?.longitude,
            //   'utc_time: ', _JSON.utc_time);

            // 回调处理数据
            callBack(_JSON.data ? _JSON.data : _JSON);

            // 更新最后成功处理时间
            lastCallbackTime = currentTime;
            processed = true;

            // 移除已处理的数据，保留剩余部分
            globalDataBuffer = globalDataBuffer.slice(jsonEnd + 1);

            // 标记数据已接收
            isDataReceived = true;

          } catch (jsonError) {
            console.log('JSON解析错误:', jsonError);
            // JSON解析失败，移除这部分数据，继续处理后面的数据
            globalDataBuffer = globalDataBuffer.slice(jsonStart + 1);
          }
        }

        // 如果缓冲区过大且没有有效数据，清空它
        if (!processed && globalDataBuffer.length > 10240) { // 10KB限制
          console.log('缓冲区过大，清空');
          globalDataBuffer = Buffer.alloc(0);
        }

      } catch (e) {
        console.log('数据处理错误:', e);
        // 发生错误时清空缓冲区
        globalDataBuffer = Buffer.alloc(0);
      }
    });

    // client.on('data', function(data) {
    //   try {
    //     // 解析JSON
    //     const jsonString = data.toString();
    //     const _JSON = JSON.parse(jsonString);
    //     isDataReceived = true;

    //     // console.log('解析成功:', 
    //     //   'latitude: ', _JSON.data?.position_3d?.latitude, 
    //     //   'longitude: ', _JSON.data?.position_3d?.longitude, 
    //     //   'utc_time: ', _JSON.data?.utc_time);

    //     // 更新最新数据
    //     latestData = _JSON.data? _JSON.data : _JSON;

    //     const currentTime = Date.now();

    //     // 如果距离上次回调超过1秒，立即执行回调
    //     if (currentTime - lastCallbackTime >= 1000) {
    //       // console.log('执行回调:', latestData);
    //       callBack(latestData);
    //       lastCallbackTime = currentTime;

    //       // 使用定时器管理器清除定时器
    //       timerManager.clearTimer('socketCallback');
    //     } else {
    //       // 如果还没到1秒，设置定时器在1秒后执行
    //       timerManager.clearTimer('socketCallback');

    //       const remainingTime = 1000 - (currentTime - lastCallbackTime);
    //       timerManager.setTimeout('socketCallback', () => {
    //         callBack(latestData);
    //         lastCallbackTime = Date.now();
    //       }, remainingTime);
    //     }
    //   } catch (jsonError) {
    //     console.log('数据处理错误:', jsonError);
    //   }
    // });

    client.on('error', function (error) {
      console.log('连接错误:', error);
    });

    client.on('close', function () {
      console.log('连接已关闭!', _socketPlayTime);

      // 如果尚未收到有效数据，重新连接
      if (!isDataReceived) {
        console.log('未成功接收数据，保留缓冲区，重新连接...');
        timerManager.setTimeout('socketReconnect', () => {
          startSocket(options, callBack);
        }, 10000);
      } else {
        console.log('已成功接收数据，重置状态');
        isDataReceived = false;
        timerManager.clearTimer('socketReconnect');
        timerManager.setTimeout('socketReconnect', () => {
          startSocket(options, callBack);
        }, 10000);
      }
    });

    return client;
  }, [timerManager]);

  // 优化Socket回调处理
  const handleSocketData = useCallback((data: SocketCallBackData) => {
    // 收集debug数据
    // setRawSocketData(data);
    // if (data.position_3d) {
    //   setDebugData({
    //     latitude: data.position_3d.latitude,
    //     longitude: data.position_3d.longitude,
    //     timestamp: new Date().toLocaleTimeString(),
    //     raw: data.position_3d,
    //   });
    // }

    if (data.position_3d) {
      if (data.motion?.speed) {
        const _speed = Number(data.motion?.speed);
        if (_speed < 3) {
          setSpeed('0');
        } else {
          setSpeed(_speed.toFixed(0) || '0');
        }
      } else {
        setSpeed('0');
      }

      if (data.rsm && data.rsm.vslInformation && data.rsm.vslInformation.overspeedStatus) {
        setVslInformation({
          overspeedStatus: data.rsm.vslInformation.overspeedStatus,
          advisorySpeed: data.rsm.vslInformation.advisorySpeed
        })
        timerManager.clearTimer('vslInformation');
        timerManager.setTimeout('vslInformation', () => {
          setVslInformation({
            overspeedStatus: 0,
            advisorySpeed: 0
          })
        }, 5000);
      }

      if (data.rsm && data.rsm.alarmData.length) {
        let pedestrianWarning = false;
        let junctionWarning = false;
        data.rsm.alarmData.forEach((item, index) => {
          if (item.alarmType === 1) {
            setPedestrianWarning(true);
            pedestrianWarning = true;
            timerManager.clearTimer('pedestrianWarning');
            timerManager.setTimeout('pedestrianWarning', () => {
              setPedestrianWarning(false);
            }, 5000);
          }
          if (item.alarmType === 2) {
            setJunctionWarning(true);
            junctionWarning = true;
            timerManager.clearTimer('junctionWarning');
            timerManager.setTimeout('junctionWarning', () => {
              setJunctionWarning(false);
            }, 5000);
          }

          if (index === data.rsm.alarmData.length - 1) {
            if (!pedestrianWarning) setPedestrianWarning(false);
            if (!junctionWarning) setJunctionWarning(false);
          }
        })
      }

      if (data.spat && data.spat.spatAlarmType === 2) {
        setSpatAlarmType(2);
        timerManager.clearTimer('spatAlarmType');
        timerManager.setTimeout('spatAlarmType', () => {
          setSpatAlarmType(0);
        }, 5000);
      }

      if (mapView) {
        setPosition({
          latitude: data.position_3d?.latitude,
          longitude: data.position_3d?.longitude
        });

        const _data = wgs84togcj02(data.position_3d?.longitude, data.position_3d?.latitude);

        setCarPosition({
          carLng: _data[0],
          carLat: _data[1]
        });

        mapView?.moveCamera(
          {
            zoom: 18,
            bearing: Number((data.motion && data.motion.heading) || 0),
            target: {
              latitude: _data[1],
              longitude: _data[0]
            },
          },
          2000
        );
      }
    }
  }, [timerManager]);

  const initSocket = useCallback((options: socketOptions) => {
    return startSocket(options, handleSocketData);
  }, [startSocket, handleSocketData]);

  const getConfig = async () => {
    // let time = 5000
    const response = await getAppState() as ApiResponse
    if (response.code == 200) {
      const state = response?.config?.shield_switch
      const imgurl = response?.config?.shield_pic
      const shield_api_time = response?.config?.shield_api_time
      // console.log(`状态：${state},`)
      const active = state == 'true' || state == true
      setShowCoverApp(active)
      setCoverAppImg(imgurl || null)
      let time = (shield_api_time ?? 5) * 1000
      timerManager.setTimeout('getConfig1', () => {
        getConfig()
      }, time);
    } else {
      timerManager.setTimeout('getConfig2', () => {
        getConfig()
      }, 5000);
    }
  }
  const swiperMediaRef = useRef<any>(null);
  const [currentIndexMedia, setCurrentIndexMedia] = useState<number>(0);
  const [tMediaSwiper, setTMediaSwiper] = useState<NodeJS.Timeout | null>(null)
  const videoRefs = useRef<any[]>([])
  const videoRef = useRef<any>(null)
  const [pausedVideo, setPausedVideo] = useState<any[]>([])
  // 接口调用频率（ms调用一次）
  const callTime = 60000
  // 路线信息
  const [infoLine, setInfoLine] = useState<{
    schedule_id: string,
    lineName: string,
    qdzName: string,
    zdzName: string,
    startStationName: string,
    endStationName: string,
    FirstTime: string,
    EndTime: string,
    price: string,
    linecode: string,
    fcsj: string,
    startStationNameActually: string,
    endStationNameActually: string,
    bcType: string
  }>({
    schedule_id: '',
    lineName: '-',
    qdzName: '-',
    zdzName: '-',
    startStationName: '-',
    endStationName: '-',
    FirstTime: '-',
    EndTime: '-',
    price: '-',
    linecode: '',
    fcsj: '',
    startStationNameActually: '',
    endStationNameActually: '',
    bcType: ''
  })
  // 排班表路线
  const [listStation, setListStation] = useState<any[]>([])
  // 换乘数据
  const [dataChangeStation, setDataChangeStation] = useState<{
    hasTime: boolean,
    list: any[]
  }>({
    // 是否已经用接口处理过到站时间
    hasTime: false,
    list: []
  })
  // 当前站点Code
  // const [currentStationId, setCurrentStationId] = useState('')
  // 当前站点索引index
  const [currentStationIndex, setCurrentStationIndex] = useState<number>(0)
  const [currentIntervalPosition, setCurrentIntervalPosition] = useState<any>(undefined)

  // 下一站数据
  const [dataNextStation, setDataNextStation] = useState<{
    station_code: string,
    station_name: string,
    arrival_distance: number | null,
    directions: any
  }>({
    station_code: '',
    station_name: '',
    arrival_distance: null,
    directions: null,
  })
  // 即将到达站点
  const [nextStationName, setNextStationName] = useState<string>('')


  // 是否到站
  const [isArrive, setIsArrive] = useState<boolean>(false)
  // 到站数据
  const [dataArriveStation, setDataArriveStation] = useState<{
    station_name: string,
    station_name_list: any[],
    hasTime: boolean,
    changeStation: any[]
  }>({
    station_name: '',
    station_name_list: [],
    // 是否已经用接口处理过到站时间
    hasTime: false,
    changeStation: []
  })

  // 调用换乘时间接口倒计时
  const [tCountDown, setTCountDown] = useState<number>(5)
  // 是否调用换乘时间接口
  const [ablechangeStationTime, setAblechangeStationTime] = useState<boolean>(false)

  // 收否更新路线
  const [isGetList, setIsGetList] = useState<boolean>(true)



  const listMediaDefault = [
    {
      trueUrl: require("./public/assets/media/young_city.mp4"),
      type: "video"
    }
  ]
  const listMediaDiShuiHu = [
    {
      UrlHost: require("./public/assets/media/dishuihu.jpg"),
      type: "pic"
    }
  ]
  const listMediaTianwenguan = [
    {
      UrlHost: require("./public/assets/media/tianwenguan.jpg"),
      type: "pic"
    }
  ]
  // 媒体列表
  const [listMedia, setListMedia] = useState<any[]>(listMediaDefault)

  // const [listMedia, setListMedia] = useState([
  //   {"exists": true, 
  //     "filepath": "/data/user/0/com.awesomeproject/files/10.jpg", 
  //     "fromUrl": "https://dev.lightcloudapps.com/lingang-bus-system/wp-content/uploads/sites/81/2024/07/0i0MXLgB07WT.jpg",
  //      "material": {"id": 10, "url": "https://dev.lightcloudapps.com/lingang-bus-system/wp-content/uploads/sites/81/2024/07/0i0MXLgB07WT.jpg"}, 
  //      "name": "aa2", 
  //      "trueUrl": "https://dev.lightcloudapps.com/lingang-bus-system/wp-content/uploads/sites/81/2024/07/0i0MXLgB07WT.jpg", "type": "pic"}, 
  //   {"exists": true, "filepath": "/data/user/0/com.awesomeproject/files/99.jpg",
  //     "fromUrl": "http://*************/wp-content/uploads/2024/08/临港中心.png",
  //     "material": {"id": 99, "url": "http://*************/wp-content/uploads/2024/08/临港中心.png"}, 
  //     "name": "aa3",
  //       "trueUrl": "http://*************/wp-content/uploads/2024/08/临港中心.png", 
  //   "type": "pic"},
  //   {"exists": true, 
  //     "filepath": "/data/user/0/com.awesomeproject/files/10.jpg", 
  //     "fromUrl": "https://dev.lightcloudapps.com/lingang-bus-system/wp-content/uploads/sites/81/2024/07/0i0MXLgB07WT.jpg",
  //      "material": {"id": 10, "url": "https://dev.lightcloudapps.com/lingang-bus-system/wp-content/uploads/sites/81/2024/07/0i0MXLgB07WT.jpg"}, 
  //      "name": "aa2", 
  //      "trueUrl": "https://dev.lightcloudapps.com/lingang-bus-system/wp-content/uploads/sites/81/2024/07/0i0MXLgB07WT.jpg", "type": "pic"}, 

  // ])
  // 判断到站的距离
  const [distanceArrive, setDistanceArrive] = useState<number>(50)
  // 到站换成循环
  const [tChangeStation, setTChangeStation] = useState<NodeJS.Timeout | null>(null)
  const scrollViewRef = useRef<any>(null);
  const scrollItemRef = useRef<any>(null);

  // 调用车辆设备日志接口频率
  const [tCarDeviceLogCountDown, setTCarDeviceLogCountDown] = useState<number>(60)
  // 是否可调用当前截屏接口
  // const [tCurrentCarDeviceLogCountDown, setTCurrentCarDeviceLogCountDown] = useState<any>(0)
  const [ableCurrentCarDeviceLog, setAbleCurrentCarDeviceLog] = useState<boolean>(false)
  // 路线上下行
  const [upDowm, setUpDowm] = useState<string | null>(null)

  // 是否计算站点距离
  // const [tPointChnageCountDown, setTPointChnageCountDown] = useState(3)
  const [ableChangestation, setAbleChangestation] = useState<boolean>(false)

  // 下一班次发车时间戳
  const [nextLineTime, setNextLineTime] = useState<number | null>(null)
  // 当前时间戳
  const [currentTime, setCurrentTime] = useState<number | null>(null)
  // 是否开启通过时间戳更新路线（用于处理提起发车导致错过路线更新节点引发的bug）
  const [canGetDateByTime, setCanGetDateByTime] = useState<boolean>(false)

  // 缓存文件存放路径
  // const videoDirectory = RNFS.DocumentDirectoryPath;
  const videoDirectory = RNFS.ExternalDirectoryPath;


  // 存放媒体素材大小等相关信息的数据
  const [jsonDataMedia, setJsonDataMedia] = useState<any>(null)


  // startSocket(function (data: SocketCallBackData) {
  //   if (data.position_3d) {
  //     setSpeed(data.motion?.speed);

  //     setWarningInfo(data.bsm?.warningInfo);

  //     setPosition({
  //       latitude: data.position_3d?.latitude,
  //       longitude: data.position_3d?.longitude
  //     });

  //     mapView.moveCamera({
  //       target: {
  //         latitude: data.position_3d?.latitude,
  //         longitude: data.position_3d?.longitude
  //       }
  //     })
  //   }
  // });


  // 路线信息
  const handleListStation = async (nbbm: string) => {
    console.log("获取路线")
    const response = await getListStation(nbbm) as ApiResponse
    // console.log(response, 333)
    // console.log(111,response)
    if ((response as any)['code'] == 200 || (response as any)['code'] == 201) {
      if (response.message) console.log(response.message)
      const schedule = response.schedule
      const linedata = {
        schedule_id: schedule?.id || '',
        lineName: schedule?.lineName || '-',
        qdzName: schedule?.qdzName || '-',
        startStationName: schedule?.startStationName || '-',
        endStationName: schedule?.endStationName || '-',
        zdzName: schedule?.zdzName || '-',
        price: schedule?.price || '-',
        FirstTime: schedule?.FirstTime || '-',
        EndTime: schedule?.EndTime || '-',
        linecode: schedule?.lineCode || '',
        fcsj: schedule?.fcsj || '',
        startStationNameActually: '',
        endStationNameActually: '',
        bcType: schedule?.bcType || ''
      }
      // console.log(schedule.fcsj,schedule.lineCode)

      const data = response.data as Station[]
      // console.log(111,data)
      // console.log(data[1]?.other_line)
      const callTime = Number(response.config?.call_time || 60) * 1000
      // console.log(callTime)
      // const callTime = 3000
      const distance_arrive = response.config?.bus_station_distance
      // 截屏频率
      const bus_api_logtime = (response.config?.bus_api_logtime || tCarDeviceLogCountDown)
      setTCarDeviceLogCountDown(bus_api_logtime)
      // 上下行
      const updowm = response.schedule?.upDowm
      setUpDowm(updowm ?? null)
      if (distance_arrive) {
        setDistanceArrive(distance_arrive)
      }

      const nextLine = response?.schedule_next || response?.schedule
      // console.log(nextLine)
      if (nextLine && nextLine?.scheduleDateStr && nextLine?.fcsj) {
        const date = `${nextLine?.scheduleDateStr || ''} ${nextLine?.fcsj || ''}`
        const timestamp = Date.parse(new Date(date).toString()) - 8 * 60 * 60 * 1000
        setNextLineTime(timestamp)
      } else {
        setNextLineTime(null)
      }
      if (data) {
        // 重置路线信息
        // console.log(data)
        setListStation(data)
      }
      linedata.startStationNameActually = data[0]?.stationName || linedata.qdzName
      linedata.endStationNameActually = data[data.length - 1]?.stationName || linedata.zdzName
      setInfoLine(linedata)
    } else {
      console.log(response.message)
    }
  }

  const [progressData, setProgressData] = useState<string>('')
  const [downloaded, setDownloaded] = useState<boolean>(true)

  const [resultDownload, setResultDownload] = useState<any[]>([])



  const existsFile = async (file: MediaItem) => {
    // 媒体文件缓存数据
    let dataMedia: Array<string> = []
    if (jsonDataMedia) {
      if (infoLine?.linecode) {
        if (jsonDataMedia[infoLine?.linecode]?.length) {
          dataMedia = [...jsonDataMedia[infoLine?.linecode]]
        }
      }
    }
    let exists = await RNFS.exists(file.filepath || '') && dataMedia.includes(file.key || '')
    file.exists = exists
    return file
  }
  // 缓存视频
  const downloadFiles = async (files: Array<MediaItem>, folderPath: string) => {
    setDownloaded(false)
    const results = await Promise.all(files.map(async (item) => await existsFile(item)));
    let dataMedia: Array<string> = []
    if (jsonDataMedia) {
      if (infoLine?.linecode) {
        if (jsonDataMedia[infoLine?.linecode]?.length) {
          dataMedia = [...jsonDataMedia[infoLine?.linecode]]
        }
      }
    }

    const result3: string[] = []
    await RNFS.mkdir(folderPath);
    for (let index = 0; index < results.length; index++) {
      const item = results[index]
      if (!item.exists) {
        console.log(`文件不存在，开始下载`)
        await RNFS.downloadFile({
          fromUrl: item.fromUrl || '',
          toFile: item.filepath || '',
          // toFile: item.tempDownloadPath,
          // background: true, // 允许在后台下载
          progressInterval: 2000,
          // progressDivider: 10,
          progress: (data) => {
            const progress = data.bytesWritten;
            const total = data.contentLength;
            const percentage = (progress / total) * 100;
            const str = `${item.key}:${percentage}%`
            setProgressData(str)
          },
        }).promise.then(result => {
          // 下载完成，记录
          if (result?.statusCode == 200) {
            const str = `${item.key}:${100}%`
            setProgressData(str)
            const str2 = `${item.key}: 下载完成`
            const result2 = [...resultDownload, str2]
            setResultDownload(result2)



            // RNFS.moveFile(item.tempDownloadPath, item.filepath)
            dataMedia.push(item.key || '')
            const dataMedia2 = Array.from(new Set(dataMedia));
            console.log(`下载的文件：${item.key}`)
            if (infoLine?.linecode) {
              const data = {
                [infoLine?.linecode]: dataMedia2
              }
              const result = {}
              Object.assign(result, jsonDataMedia, data)
              console.log(result)
              setJsonDataMedia(result)
            }
          } else {
            const str = `错误1:${item.key}:${JSON.stringify(result)}`
            setProgressData(str)
            const str2 = `${item.key}: 下载出错`
            result3.push(str2)
            setResultDownload(result3)
          }
        }).catch((err) => {
          const str = `错误2:${item.key}:${JSON.stringify(err)}`
          setProgressData(str)
          const str2 = `${item.key}: 下载出错`
          result3.push(str2)
          setResultDownload(result3)
        })
      } else {
        // console.log('文件已存在')
        const str = `${item.key}: 文件已存在`
        console.log(str)
        result3.push(str)
        setResultDownload(result3)
      }
    }
    // console.log('文件处理完成')
    setDownloaded(true)
  };

  // useEffect(() => {
  //   if (listStation.length && position.latitude && position.longitude) {
  //     if (tPointChnageCountDown == 0) {
  //       const pointCar:Point = {
  //         lat: position.latitude,
  //         lng: position.longitude
  //       }
  //       handleCurrentInfo(listStation)
  //     }
  //   }
  // }, [position.latitude, position.longitude])

  // 站点位置计算 3s一次
  useEffect(() => {
    if (ableChangestation && listStation.length && position.latitude && position.longitude) {
      console.log('站点计算')
      handleCurrentInfo(listStation)
    }
    timerManager.setTimeout('stationCalculation', () => {
      const result = !ableChangestation
      setAbleChangestation(ableChangestation => result)
    }, 1000);
    return () => timerManager.clearTimer('stationCalculation')
  }, [ableChangestation, timerManager])

  // 处理错过路线更新节点bug
  useEffect(() => {
    console.log(`当前时间：${currentTime},下一站时间：${nextLineTime},是否按时间：${canGetDateByTime}`)
    // if (nextLineTime && currentTime) {
    if (carID_form) {
      handleListStation(carID_form)
    }
    // }
  }, [nextLineTime, currentTime, carID_form, canGetDateByTime])

  // 路线更新后，初始站点更新为默认状态首站点
  useEffect(() => {
    console.log(`路线发生变化:${infoLine.fcsj}`)
    if (infoLine.bcType == 'in') {
      setCanGetDateByTime(true)
    } else {
      setCanGetDateByTime(false)
    }
    if (listStation.length) {
      setCurrentStationIndex(0)
      // handleMedia()
    }
  }, [infoLine.fcsj])
  useEffect(() => {
    if (listStation.length) {
      // 首尾站点
      if (currentStationIndex == listStation.length - 1 || currentStationIndex == 0) {
        setIsGetList(true)
      } else {
        setIsGetList(false)
      }

      if (currentStationIndex == listStation.length - 1) {
        console.log("终点站")
        setCanGetDateByTime(true)
        setIsArrive(true)
        timerManager.setTimeout('arriveDisplay1', () => {
          setIsArrive(false)
        }, 5000);
        // 下一站信息
        const datanextstation = {
          station_code: '',
          station_name: '',
          arrival_distance: null,
          directions: null
        }
        setDataNextStation(datanextstation)
        setNextStationName('')
        setDataChangeStation({
          // 是否已经用接口处理过到站时间
          hasTime: false,
          list: []
        })
      }

      if (currentStationIndex != 0) {
        setIsArrive(true)
        timerManager.setTimeout('arriveDisplay2', () => {
          setIsArrive(false)
        }, 10000);
      }


      // handleMedia()
    }
  }, [currentStationIndex, timerManager])
  // 换乘时间
  const handleChangeStation = async (linecode: any, stationCode: any, stationName: string, directions: any) => {
    const response = await getCurrentInfo(linecode, stationCode, stationName, directions) as ApiResponse
    if (response.code == 200) {
      const data = response.data as any[]
      data.map(item => {
        if (item.car) {
          item.time = Math.ceil(item.car.time / 60)
          item.linename = `${item.name}(约${item.time}分钟)`
        } else {
          item.linename = item.name
        }
        return item
      })
      return data
    } else {
      return []
    }
  }
  // 当前车辆信息
  const handleCurrentInfo = async (stations: Station[]) => {
    if (stations.length && position.latitude && position.longitude) {
      const pointCar: Point = {
        lat: position.latitude,
        lng: position.longitude
      }
      let currentIndex = currentStationIndex
      let arrive = false
      for (let i = 0; i < stations.length; i++) {
        const pointStation: Point = {
          lat: stations[i]?.lat,
          lng: stations[i]?.lon
        }
        const distance = getDistanceBetweenCoordinates(pointCar, pointStation)
        // console.log(`当前站点:${stations[i].stationName},当前坐标：${position.latitude},${position.longitude},距离：${distance},${distanceArrive}`)
        if (distance <= distanceArrive) {
          // 是否存在与此站点相同的站点
          const hasRepeat = stations.filter(item => {
            return item.stationCode == stations[i]?.stationCode
          }).length >= 2
          if (hasRepeat && currentIndex > i) {
            continue
          } else {
            currentIndex = i
            console.log(`当前站点：${currentIndex}到站,坐标${position.latitude},${position.longitude}`)
            arrive = true
            break
          }
        }
      }
      // 到站
      if (arrive) {
        const arriveStationInfo = stations[currentIndex]

        // 下一站地铁换乘
        const changeStationMetro = arriveStationInfo?.metro_arr?.map(item => {
          item.type = 'metro'
          return item
        }) || []

        const handleDataArrive = async function (linecode: any, stationCode: any, stationName: any, directions: any) {

          if (linecode && stationCode && stationName && directions != null) {
            const list = await handleChangeStation(linecode, stationCode, stationName, directions)
            if (list.length) {
              setDataArriveStation({
                station_name: stations[currentIndex].stationName,
                station_name_list: stations[currentIndex].stationName2?.length ? stations[currentIndex].stationName2 : [stations[currentIndex].stationName],
                hasTime: true,
                changeStation: [...list, ...changeStationMetro]
              })
            } else {
              // 同一站点，不重复设置当前换站信息
              if (arriveStationInfo.stationName != dataArriveStation.station_name) {
                // 换乘信息
                const dataChangeStation = arriveStationInfo?.other_line?.map((item, index) => {
                  item.type = 'bus'
                  return item
                }) || []
                const dataArrive = {
                  station_name: stations[currentIndex].stationName,
                  station_name_list: stations[currentIndex].stationName2?.length ? stations[currentIndex].stationName2 : [stations[currentIndex].stationName],
                  hasTime: false,
                  changeStation: [...dataChangeStation, ...changeStationMetro],
                }
                setDataArriveStation(dataArrive)
              }
            }
          } else {
            // 同一站点，不重复设置当前换站信息
            if (arriveStationInfo.stationName != dataArriveStation.station_name) {
              // 换乘信息
              const dataChangeStation = arriveStationInfo?.other_line?.map((item, index) => {
                item.type = 'bus'
                return item
              }) || []
              const dataArrive = {
                station_name: stations[currentIndex].stationName,
                station_name_list: stations[currentIndex].stationName2?.length ? stations[currentIndex].stationName2 : [stations[currentIndex].stationName],
                hasTime: false,
                changeStation: [...dataChangeStation, ...changeStationMetro],
              }
              setDataArriveStation(dataArrive)
            }
          }
        }

        const linecode = infoLine.linecode
        const stationCode = arriveStationInfo.stationCode
        const stationName = arriveStationInfo.stationName
        const directions = arriveStationInfo.directions

        // 同一站点，不重复设置当前换站信息
        if (arriveStationInfo.stationName != dataArriveStation.station_name) {
          await handleDataArrive(linecode, stationCode, stationName, directions)
        } else {
          if (!dataArriveStation.hasTime) {
            await handleDataArrive(linecode, stationCode, stationName, directions)
          }
        }
        // if (currentIndex != 0 && currentIndex != stations.length - 1) {
        //   setIsArrive(true)
        // }
      } else {
        // 远离车站
        setIsArrive(false)
        const dataArrive = {
          station_name: '',
          station_name_list: [],
          hasTime: false,
          changeStation: [],
        }
        setDataArriveStation(dataArrive)
      }
      // 最后一站到达后，不再计算距离等数据
      if (currentIndex != stations.length - 1) {
        const index_next = Math.min(currentIndex + 1, stations.length - 1)

        const nextStationInfo = stations[index_next]
        if (nextStationInfo) {
          const pointNextStation: Point = {
            lat: nextStationInfo?.lat,
            lng: nextStationInfo?.lon
          }
          const distance = await getDistanceBetweenCoordinates(pointCar, pointNextStation)
          // 下一站信息
          const datanextstation = {
            station_code: nextStationInfo?.stationCode || '',
            station_name: nextStationInfo?.stationName || '',
            arrival_distance: distance,
            directions: nextStationInfo?.directions
          }
          setDataNextStation(datanextstation)
          if (nextStationName != nextStationInfo?.stationName) {
            setNextStationName(nextStationInfo?.stationName)
          }

          if (ablechangeStationTime) {
            const linecode = infoLine.linecode
            const stationCode = datanextstation.station_code
            const stationName = datanextstation.station_name
            const directions = datanextstation.directions
            if (linecode && stationCode && stationName && directions != null) {
              const list = await handleChangeStation(linecode, stationCode, stationName, directions)
              setAblechangeStationTime(false)
              // 下一站地铁换乘
              const changeStationMetro = nextStationInfo?.metro_arr?.map(item => {
                item.type = 'metro'
                return item
              }) || []
              if (list.length) {
                setDataChangeStation({
                  hasTime: true,
                  list: [...list, ...changeStationMetro]
                })
              } else {
                // 下一站换乘信息
                const changeStation = nextStationInfo?.other_line?.map((item, index) => {
                  item.type = 'bus'
                  return item
                }) || []
                setDataChangeStation({
                  hasTime: false,
                  list: [...changeStation, ...changeStationMetro]
                })
              }

            }
          } else if (!dataChangeStation.hasTime) {
            // 下一站换乘信息
            const changeStation = nextStationInfo?.other_line?.map((item, index) => {
              item.type = 'bus'
              return item
            }) || []
            // 下一站地铁换乘
            const changeStationMetro = nextStationInfo?.metro_arr?.map(item => {
              item.type = 'metro'
              return item
            }) || []
            setDataChangeStation({
              hasTime: false,
              list: [...changeStation, ...changeStationMetro]
            })
          }
        }
      }
      setCurrentStationIndex(currentIndex)
    }
  }

  useEffect(() => {
    if (isArrive) {
      if (tChangeStation) {
        timerManager.clearTimer('changeStationRotation')
      }

      if (dataArriveStation.changeStation.length > 4) {
        function moveToEnd(arr: Array<JSON>) {
          if (arr.length <= 4) return arr; // 如果数组长度不足四个，则直接返回原数组

          // 使用slice方法获取前四个元素，第二个参数传入-4表示从倒数第四个元素开始
          const firstFour = arr.slice(0, 3);
          // 使用slice方法获取除前四个之外的剩余元素
          const rest = arr.slice(3);
          // 将两部分合并，即将前四个元素放到数组末尾
          return rest.concat(firstFour);
        }
        timerManager.setTimer('changeStationRotation', () => {
          dataArriveStation.changeStation = moveToEnd(dataArriveStation.changeStation)
          const result = JSON.parse(JSON.stringify(dataArriveStation))
          setDataArriveStation(result)
        }, 3000);
        setTChangeStation(setTimeout(() => { }, 0))
      }
    } else {
      if (tChangeStation) {
        timerManager.clearTimer('changeStationRotation')
      }
    }
  }, [isArrive, timerManager])

  // 获取坐标间的距离
  const getDistanceBetweenCoordinates = (startpoint: Point, endPoint: Point) => {
    const radius = 6371; // 地球半径，单位为千米

    const lat1 = startpoint.lat * (Math.PI / 180);
    const lon1 = startpoint.lng * (Math.PI / 180);
    const lat2 = endPoint.lat * (Math.PI / 180);
    const lon2 = endPoint.lng * (Math.PI / 180);

    const dLat = lat2 - lat1;
    const dLon = lon2 - lon1;

    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1) * Math.cos(lat2) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = radius * c;

    return Math.ceil(distance * 1000);
  }

  // 处理媒体轮播
  const handleSwiper = (indexCurrent: number) => {
    console.log('开始轮播')
    if (tMediaSwiper) {
      clearTimeout(tMediaSwiper)
      setTMediaSwiper(null)
    }
    if (swiperMediaRef.current) {
      // let length = listMedia.length
      // console.log('开始滚动,长度：', length)1 3
      let length = listMedia.length
      indexCurrent = indexCurrent + 1
      timerManager.setTimeout('mediaSwiper', () => {
        if (indexCurrent <= length - 1) {
          setCurrentIndexMedia(indexCurrent)
        } else {
          indexCurrent = 0
          setCurrentIndexMedia(0)
        }
        swiperMediaRef.current.scrollToIndex({ index: indexCurrent, animated: true });
        setTMediaSwiper(null)
        // 轮播到视频，暂停轮播直到视频播放完
        if (listMedia[indexCurrent].type != 'video') {
          handleSwiper(indexCurrent)
        }
      }, 5000);
      setTMediaSwiper(setTimeout(() => { }, 0))
    }
  }

  // 处理历史缓存文件
  const handleMedialistAPP = (videosToKeep: Array<{ key: string, name: string }>) => {
    // 媒体文件缓存数据
    let dataMedia: Array<string> = []
    if (jsonDataMedia) {
      if (infoLine?.linecode) {
        if (jsonDataMedia[infoLine?.linecode]?.length) {
          dataMedia = [...jsonDataMedia[infoLine?.linecode]]
        }
      }
    }
    // 获取文件系统根目录下的所有文件和文件夹
    // const folderPath = `${videoDirectory}`
    const folderPath = `${videoDirectory}/${infoLine.linecode}`
    const videosToKeepName = videosToKeep.map(item => {
      return item.name
    })
    console.log(videosToKeepName)
    RNFS.readDir(`${folderPath}`)
      .then(files => {
        // 遍历所有文件
        files.forEach(file => {
          // 检查文件是否为视频或图片且不在保留名单中
          if (file.isFile() && (file.name.endsWith('.mp4') || file.name.endsWith('.jpg')) && !videosToKeepName.includes(file.name)) {
            const filePath = `${folderPath}/${file.name}`;
            // 删除文件
            RNFS.unlink(filePath)
              .then(() => {
                console.log('File deleted:', filePath)
                let dataMedia2 = Array.from(new Set(dataMedia));
                dataMedia2 = dataMedia2.filter(item => item != file.name)

                if (infoLine?.linecode) {
                  const data = {
                    [infoLine?.linecode]: dataMedia2
                  }
                  const result = {}
                  Object.assign(result, jsonDataMedia, data)
                  // console.log(result)
                  setJsonDataMedia(result)
                }
              })
              .catch(err => console.error('Error deleting file:', err));
          }
        });
      })
      .catch(err => console.error('Error reading directory:', err));
  }

  const handleMedia = async () => {
    if (listStation.length) {
      // const index_next = Math.min(currentStationIndex + 1, listStation.length - 1)
      const index = currentStationIndex

      // 暂时禁用掉素材变化功能,变为本地
      // 滴水湖站展示的素材
      // let list2 = listMedia
      // if (listStation[index_next].stationCode == '122802.5') {
      //   // setListMedia(listMediaDiShuiHu)
      //   list2 = listMediaDiShuiHu
      // } else if (listStation[index_next].stationCode == '122803') {
      //    // 环湖西二路楠木路展示的素材
      //   // setListMedia(listMediaTianwenguan)
      //   list2 = listMediaTianwenguan
      // } else {
      //   list2 = listMediaDefault
      //   // setListMedia(listMediaDefault)
      // }
      // // let list2 = listMedia
      // if (list2) {
      //   const currentIndex = Math.min(currentStationIndex + 1, listStation.length - 1)
      //   // 如果媒体列表发生变化,切下一站点与下载的媒体资源所在站点相同
      //   try {
      //     if (JSON.stringify(list2) !== JSON.stringify(listMedia) && index_next == currentIndex) {
      //       setListMedia([...list2])
      //       setCurrentIndexMedia(0)
      //     }
      //   } catch (error) {
      //     console.log(error)
      //   }
      // }

      // return false

      const medialist = listStation[index]?.station_data || []

      const folderPath = `${videoDirectory}/${infoLine.linecode}`
      medialist.map((item: MediaItem) => {
        const key = item.material.id || item.name
        const filepath = `${folderPath}/${key}${item.type == 'video' ? '.mp4' : '.jpg'}`
        item.fromUrl = item.material.url
        item.filepath = filepath
        item.key = `${key}`
        item.linecode = infoLine.linecode
        item.trueUrl = item.type == 'video' ? filepath : `file://${filepath}`
        return item
      })

      const results = await Promise.all(medialist.map(async (item: any) => await existsFile(item)));

      const result = results.filter(item => {
        return item.exists
      })

      // console.log(111,result)

      let list = listMedia

      if (result.length) {
        list = result
      } else {
        list = listMediaDefault
      }
      if (list) {
        const currentIndex = Math.min(currentStationIndex + 1, listStation.length - 1)
        // 如果媒体列表发生变化,切下一站点与下载的媒体资源所在站点相同
        try {
          if (JSON.stringify(list) !== JSON.stringify(listMedia)) {
            setCurrentIndexMedia(0)
            setListMedia([...list])
          }
        } catch (error) {
          console.log(error)
        }
      }
    }
  }

  useEffect(() => {
    try {
      console.log(`重新渲染轮播: ${listMedia.length} ${JSON.stringify(listMedia)}`)
      if (tMediaSwiper) {
        clearTimeout(tMediaSwiper)
        setTMediaSwiper(null)
      }
      if (swiperMediaRef.current) {
        // swiperMediaRef.current.scrollTo({ index: 0, animated: true });
        swiperMediaRef.current.scrollToIndex({ index: 0, animated: false });
      }
      if (listMedia.length) {
        if (listMedia[0].type != 'video') {
          handleSwiper(0)
        }
      }
    } catch (error) {
      console.log(error)
    }
  }, [listMedia])

  const hanldeDownload = async () => {
    console.log("处理媒体素材")
    let list: MediaItem[] = []
    for (let i = 0; i < listStation.length; i++) {
      list.push(...(listStation[i].station_data || []))
    }

    let result: MediaItem[] = []
    for (let i = 0; i < list.length; i++) {
      const hasItem = result.filter(item => {
        return item.material?.url == list[i].material?.url
      }).length
      if (!hasItem) {
        result.push(list[i])
      }
    }
    const folderPath = `${videoDirectory}/${infoLine.linecode}`
    result.map(item => {
      item.fromUrl = item.material.url
      const key = item.material.id || item.name
      item.filepath = `${folderPath}/${key}${item.type == 'video' ? '.mp4' : '.jpg'}`
      item.key = `${key}`
      item.linecode = infoLine.linecode
      return item
    })
    await downloadFiles(result, folderPath)

    const videosToKeep = result.map(item => {
      const name = item.key
      if (item.type == 'video') {
        return {
          key: name,
          name: `${name}.mp4`
        }
      } else {
        return {
          key: name,
          name: `${name}.jpg`
        }
      }
    })
    handleMedialistAPP(videosToKeep as any)
  }

  // 路线变化，重新处理需要下载的数据
  useEffect(() => {
    if (listStation.length) {
      // hanldeDownload()
    }
  }, [infoLine.linecode])

  useEffect(() => {
    // console.log(`重新下载媒体素材：${downloaded}`)
    // if (listStation.length && downloaded) {
    //   const t = setTimeout(() => {
    //     hanldeDownload()
    //   }, 10000);
    //   return () => clearTimeout(t)
    // }
    // timerManager.setTimer('downloadCheck', () => {
    //   if (listStation.length && downloaded) {
    //     hanldeDownload()
    //   }
    // }, 3600000);
    return () => timerManager.clearTimer('downloadCheck')
  }, [downloaded, timerManager])

  useEffect(() => {
    timerManager.setTimeout('countDown', () => {
      const result = tCountDown == 0 ? 5 : 0
      if (tCountDown == 5) {
        setAblechangeStationTime(true)
      }
      setTCountDown(tCountDown => result)
    }, 5000);
    return () => timerManager.clearTimer('countDown')
  }, [tCountDown, timerManager])

  useEffect(() => {
    const handle = async () => {
      console.log(`当前的缓存数据：${JSON.stringify(jsonDataMedia)}`)
      const data = jsonDataMedia
      await storeData(`DataMedia`, JSON.stringify(data))
    }
    handle()
  }, [jsonDataMedia])


  useEffect(() => {
    if (ableCurrentCarDeviceLog != null) {
      if (ableCurrentCarDeviceLog && upDowm != null && infoLine.linecode && listStation.length) {
        const obg = {
          nbbm: carID_form,
          schedule_id: infoLine.schedule_id,
          linecode: infoLine.linecode,
          stationcode: listStation[currentStationIndex].stationCode,
          directions: upDowm
        }
        setCarStatus(obg)
      }
      timerManager.setTimeout('carDeviceLog', () => {
        const result = !ableCurrentCarDeviceLog
        setAbleCurrentCarDeviceLog(ableCurrentCarDeviceLog => result)
      }, tCarDeviceLogCountDown * 1000);
    }
  }, [ableCurrentCarDeviceLog, timerManager])

  React.useEffect(() => {
    HttpBaseGet("/LingangBus/v1/scenic_spot").then((data: any) => {
      if (data.code === 200) {
        setMarkers(data.data)
      }
    })
    getConfig();

    timerManager.setTimer('currentTime', () => {
      const timestamp = Date.parse(new Date().toString())
      setCurrentTime(timestamp)
    }, 5000);

    // initSocket({
    //   host: '***********',
    //   port: 7878
    // });

    // 初始化缓存媒体文件
    // initData(`DataMedia`, (val) => {
    //   console.log(`mediaData:${val}`)
    //   if (val) {
    //     setJsonDataMedia(JSON.parse(val))
    //   }
    // })

    initData('carID', (val) => {
      setCarIdForm(val);
      NetInfo.addEventListener(state => {
        setNetState(state.isConnected || false)
      });
    });

    initData('ip', (val) => {
      let _ip = val;
      setIpForm(val);
      initData('port', (val) => {
        let _port = val;
        setProtForm(val);
        if (_ip && _port) {
          // console.log(`初始化连接：${_ip}:${_port}`);
          Socket = initSocket({
            host: _ip,
            port: Number(_port)
          });
        }
      });
    });

    AMapSdk.init(
      Platform.select({
        android: "8972bda6fb2e829e0b5f32a098aeb8e0"
      })
    );
    return () => timerManager.clearTimer('currentTime')
  }, [timerManager]);
  useEffect(() => {
    if (netState && carID_form) {
      // 终点站反复调用路线数据
      timerManager.setTimeout('listStation', () => {
        handleListStation(carID_form)
      }, 10000);
    } else if (!netState) {
      timerManager.clearTimer('listStation')
    }
  }, [netState, carID_form, timerManager])

  useEffect(() => {
    // 如果处于首尾站点
    if (isGetList && carID_form) {
      timerManager.clearTimer('listStation')
      // 终点站反复调用路线数据
      timerManager.setTimeout('listStation', () => {
        handleListStation(carID_form)
      }, 10000);
    } else if (!isGetList) {
      timerManager.clearTimer('listStation')
    }
  }, [isGetList, carID_form, timerManager])

  const onEnd = (index: number, ref: any) => {
    if (tMediaSwiper) {
      timerManager.clearTimer('mediaSwiper')
      setTMediaSwiper(null)
    }
    handleSwiper(index)
  }
  const onProgress = (data: any) => {
    console.log(data)
    if (data.currentTime > 0 && tMediaSwiper) {
      timerManager.clearTimer('mediaSwiper')
      setTMediaSwiper(null)
    }
  }

  // const listPoints = [
  //   {
  //     latitude: 30.916107,
  //     longitude: 121.91396
  //   },
  //   {
  //     latitude: 30.913834,
  //     longitude: 121.9188
  //   },
  //   {
  //     latitude: 30.905638,
  //     longitude: 121.91345
  //   },
  //   {
  //     latitude: 30.898788,
  //     longitude: 121.91252
  //   },
  //   {
  //     latitude: 30.88789,
  //     longitude: 121.91676
  //   },
  //   {
  //     latitude: 30.88933,
  //     longitude: 121.9228
  //   },
  //   {
  //     latitude: 30.895437,
  //     longitude: 121.918884
  //   },
  //   {
  //     latitude: 30.901314,
  //     longitude: 121.918304
  //   },
  //   {
  //     latitude: 30.903791,
  //     longitude: 121.91876
  //   },
  //   {
  //     latitude: 30.910168666666664,
  //     longitude: 121.92611833333333
  //   },
  //   {
  //     latitude: 30.915949,
  //     longitude: 121.91846
  //   },



  //   {
  //     latitude: 30.920456,
  //     longitude: 121.91168
  //   },
  //   {
  //     latitude: 30.917587,
  //     longitude: 121.910866
  //   },
  //   {
  //     latitude: 30.916107,
  //     longitude: 121.91396
  //   },
  // ]
  // const [indextest, setindextest] = useState<number>(0)
  // const test = async () => {
  //   // swiperMediaRef.current.scrollTo({ index: 2, animated: true });

  //   // index = index + 1 ;
  //   // console.log(222, index)
  //   // swiperMediaRef.current.scrollToIndex({ index: 2, animated: false });
  //   // const data = {
  //   //   "211208":["496","498","502","500","501","499"],
  //   //   "803111":["496","473"]
  //   // }
  //   // await storeData('DataMedia', '')

  //   // initData(`DataMedia`, (val) => {
  //   //   console.log(`mediaData:${val}`)
  //   //   if (val) {
  //   //     setJsonDataMedia(JSON.parse(val))
  //   //   }
  //   // })



  //   let index = indextest
  //   const currentPoint = listPoints[index]
  //   setPosition(currentPoint)

  //   if (index == listPoints.length - 1) {
  //     index = 1
  //   }
  //   setindextest(index + 1)

  //   // {"211208":["500","499","498"]}



  //   // const currentPoint = {
  //   //   latitude: 30.920456,
  //   //   longitude: 121.91168
  //   // }
  //   // const currentPoint = {
  //   //   latitude: 30.917587,
  //   //   longitude: 121.910866
  //   // }


  //   // setPosition(currentPoint)

  //   // const a = {a: [1,2,3]}
  //   // const b = {a: [4,5,6]}

  //   // const c = Object.assign({}, a, b)
  //   // console.log(c)
  //   // await storeData(`test`, '1111')
  //   // initData(`test`, (val) => {
  //   //   console.log(val)
  //   // })
  //   // await storeData(`test`, 'hhhh')
  //   // initData(`test`, (val) => {
  //   //   console.log(val)
  //   // })

  // }
  // const test2 = async () => {
  //   swiperMediaRef.current.scrollBy(2, false)
  // }

  const renderItemMedia = ({ item, index }: { item: any, index: number }) => {
    if (item.type == 'pic') {
      if (item.trueUrl) {
        return (
          <View style={styles.slide1} key={index}>
            <Image source={{ uri: item.trueUrl }} style={stylesMedia.image} resizeMode='cover' />
          </View>
        )
      } else {
        return (
          <View style={styles.slide1} key={index}>
            <Image source={item.UrlHost} style={stylesMedia.image} resizeMode='cover' />
          </View>
        )
      }
      // return (
      //   <View style={styles.slide1} key={index}>
      //     {/* <Image source={{uri: item.trueUrl}} style={stylesMedia.image} resizeMode='cover' /> */}
      //     <Image source={ item.UrlHost} style={stylesMedia.image} resizeMode='cover' />
      //   </View>
      // )
    }
    else if (item.type == 'video') {
      return (
        <View style={styles.slide1} key={index}>
          <Video
            ref={swipeable => videoRefs.current[index] = swipeable}
            resizeMode='cover'
            repeat={true}
            paused={currentIndexMedia != index}
            onEnd={() => onEnd(index, videoRefs.current[index])}
            // onProgress={onProgress}
            muted={true}
            style={stylesMedia.video}
            source={{ uri: item.trueUrl }}
          />
        </View>
      )
    }
  }


  return (
    <SafeAreaView style={styles.containerBus}>

      <ImageBackground style={styles.container} source={require('./public/assets/bg.jpg')}>
        <View style={stylesTitle.container}>
          <Image source={require('./public/assets/bg-title.png')} style={stylesTitle.title} />
          <Icon name="settings" style={stylesTitle.config} onPress={() => setDialogVisible(true)} size={20} />
          {/* <Text>{position.latitude}</Text> */}
        </View>

        {/* <Button onPress={test} title='click'></Button> */}
        {/* <View>
         <Text style={{fontSize: scaleSize(20)}}>{progressData}</Text>
         {resultDownload.map(item => {
          return <Text key={item} style={{fontSize: scaleSize(20)}}>{item}</Text>
         })}
        </View> */}
        {/* <Text style={{fontSize: scaleSize(20)}}>当前站点：{currentStationIndex}</Text> */}
        {/* <Text style={{fontSize: scaleSize(20)}}>缓存数据：{JSON.stringify(jsonDataMedia)}</Text> */}
        {/* <Button onPress={test4} title='终点站'></Button> */}
        {/* <Button onPress={() => {
           const _data = wgs84togcj02(121.91851509999999, 30.902738500000002);

           setCarPosition({
             carLat: _data[1],
             carLng: _data[0]
           });
   
           mapView?.moveCamera(
             {
               zoom: 18,
               bearing: 11.175000000000001,
               target: { 
                 latitude: _data[1],
                 longitude: _data[0]
               },
             },
             2000
           );
        }} title="click"></Button> */}

        <View style={[stylesMain.container, styles.shadomcontainer, isArrive ? stylesMain.inActive : undefined]}>
          <RouterLine
            height={windowHeight * 0.3}
            infoLine={infoLine}
            listStation={listStation}
            nextStationName={nextStationName}
            // currentStationId= {currentStationId}
            currentStationIndex={currentStationIndex}
            dataChangeStation={dataChangeStation.list}
            dataNextStation={dataNextStation}
          ></RouterLine>

          <View style={style.mapview}>
            <MapView
              ref={(ref: MapView) => (mapView = ref)}
              zoomControlsEnabled={false}
              style={
                { width: windowWidth, height: '100%' }
              }
              mapType={MapType.Navi}
              headingFilter={30}
              initialCameraPosition={{
                tilt: 70,
                target: {
                  latitude: position?.latitude,
                  longitude: position?.longitude
                },
                zoom: 18,
              }}>
              <Marker
                // icon={require('./images/car.png')}
                zIndex={10}
                position={{ latitude: carPosition?.carLat, longitude: carPosition?.carLng }}
              >
                <Image source={require('./images/car.png')} style={{
                  width: 24,
                  height: 24
                }} />
              </Marker>

              <View>
                {
                  markers.length ? markers.map((item: any, index: number): React.JSX.Element => {
                    return (
                      <Marker
                        key={index}
                        position={{ latitude: +item?.lat, longitude: +item?.lon }}
                      >
                        <View style={style.markerBox}>
                          <Text style={style.markerText} >{item.title}</Text>
                          {
                            item.pic && <Image source={{
                              uri: item.pic
                            }} style={style.mapcar} />
                          }
                        </View>
                      </Marker>
                    )
                  }) : ""
                }
              </View>
            </MapView>

            <Image source={require('./images/car-bg.jpg')} style={style.mapimg} />

            <Text style={style.mapspeed}>{speed}</Text>
            <Text style={style.mapspeedaunit}>KM/H</Text>

            <View style={style.notification}>
              <View style={{ borderRadius: 100, marginBottom: 10, backgroundColor: 'rgba(255, 255, 255, 0.5)' }}>
                {isPlaying ? <CountdownCircleTimer
                  size={50}
                  trailColor={'#fff'}
                  isPlaying={isPlaying}
                  strokeWidth={5}
                  onComplete={() => {
                    setIsPlaying(false);
                    setRemainingTime(0);
                    // setTimeout(() => {
                    //   setIsPlaying(true);
                    //   setRemainingTime(30);  
                    // }, 0)
                  }}
                  duration={remainingTime}
                  colors={[remainingTimeRgb, remainingTimeRgb]}
                  colorsTime={[remainingTime, 0]}
                >
                  {({ remainingTime }) => <Text style={{ color: remainingTimeRgb, fontSize: 16, fontWeight: 'bold' }}>{remainingTime} s</Text>}
                </CountdownCircleTimer> : ''}
              </View>

              {vslInformation.overspeedStatus ? (<View style={style.notificationItem}>
                <Icon name='warning' size={20} color='#f8d849' />
                <Text style={style.notificationItemText}>限速预警</Text>
                <Text style={style.notificationItemText}>{vslInformation.advisorySpeed} km/h</Text>
              </View>) : ""}

              {spatAlarmType ? (<View style={style.notificationItem}>
                <Icon name='warning' size={20} color='#f8d849' />
                <Text style={style.notificationItemText}>闯红灯预警</Text>
              </View>) : ""}

              {pedestrianWarning ? (<View style={style.notificationItem}>
                <Icon name='warning' size={20} color='#f8d849' />
                <Text style={style.notificationItemText}>碰撞行人预警</Text>
              </View>) : ""}

              {junctionWarning ? (<View style={style.notificationItem}>
                <Icon name='warning' size={20} color='#f8d849' />
                <Text style={style.notificationItemText}>路口碰撞预警</Text>
              </View>) : ""}
            </View>
          </View>
        </View>
        <View style={[stylesMedia.container, isArrive ? stylesMain.inActive : undefined]}>
          <FlatList
            style={styles.wrapper}
            ref={swiperMediaRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            data={listMedia}
            renderItem={renderItemMedia as any}
            viewabilityConfig={{
              itemVisiblePercentThreshold: 10,
            }}
            onScrollToIndexFailed={(info) => {
              console.log('滚动到索引失败：', info);
            }}
          // scrollToIndex={index}
          />



          {/* <Swiper 
            style={styles.wrapper} 
            showsButtons={false}
            showsPagination={false}
            autoplay={false}
            ref={swiperMediaRef}
            loop={true}
          >
            {listMedia.map((item,index) => {
              if (item.type == 'pic') {
                return (
                  <View style={styles.slide1} key={index}>
                    <Image source={{uri: item.trueUrl}} style={stylesMedia.image} resizeMode='cover' />
                  </View>
                )
              } 
              else if (item.type == 'video') {
                return (
                  <View style={styles.slide1} key={index}>
                    <Video
                    ref={ swipeable => videoRefs.current[index] = swipeable }
                    resizeMode='cover'
                    repeat={true}
                    paused={currentIndexMedia != index}
                    onEnd={() => onEnd(index, videoRefs.current[index])}
                    onProgress={() => onProgress(index)}
                    muted={true}
                    style={stylesMedia.video}
                    source={{uri: item.trueUrl }}
                    />
                  </View>
                )
              }
            })}
          </Swiper> */}
        </View>
      </ImageBackground>

      <ImageBackground style={[stylesArrive.container, isArrive ? stylesArrive.active : undefined]} source={require('./public/assets/bg-arrive2.jpg')}>
        <ImageBackground style={stylesArrive.containerTop} source={require('./public/assets/bg-arrive-top.png')} resizeMode='contain'>
          {/* <Image style={stylesArrive.TopImg} source={{ uri: coverAppImg }}  resizeMode='cover' /> */}
          <Text style={stylesArrive.tipsTop}>请携带好个人随身物品</Text>
          <View style={stylesArrive.stationContainer}>
            <View style={stylesArrive.mainTextContainer}>
              {
                dataArriveStation.station_name_list.map((item, index) => {
                  return (
                    <Text style={stylesArrive.mainText} key={index} numberOfLines={1} adjustsFontSizeToFit>{item}</Text>
                  )
                })
              }
              {/* <Text style={stylesArrive.mainText} numberOfLines={1} adjustsFontSizeToFit>{dataArriveStation.station_name}</Text>
              <Text style={stylesArrive.mainText} numberOfLines={1} adjustsFontSizeToFit>{dataArriveStation.station_name}</Text>
              <Text style={stylesArrive.mainText} numberOfLines={1} adjustsFontSizeToFit>{dataArriveStation.station_name}</Text> */}
            </View>

            <Text style={[stylesArrive.mainText, stylesArrive.textCenter]}>到了</Text>
          </View>
          {/* <Image style={stylesArrive.TopImg} source={require('./public/assets/bg-arrive-mouse.png')}  resizeMode='contain' /> */}
        </ImageBackground>
        <View style={[dataArriveStation.changeStation.length ? stylesArrive.active : stylesArrive.containerBottom]}>
          <View style={stylesArrive.tipsBottomContainer}>
            <Image style={stylesArrive.BottomImg} source={require('./public/assets/bg-arrive-bottom.png')} />
            <Text style={stylesArrive.tipsBottom}>此站可换乘</Text>
          </View>
          <View style={stylesArrive.ChangeStationContainer}>

            {dataArriveStation.changeStation.map((item, index) => {
              let isOdd = (index + 1) % 2 === 1
              // 只展示前四个，剩下的轮播
              if (index <= 3) {
                if (isOdd) {
                  return (
                    <View style={stylesArrive.ChangeStationItem} key={index}>
                      <Image style={stylesArrive.ChangeStationImg} source={require('./public/assets/bg-arrive-border2.png')} />
                      <Text style={stylesArrive.ChangeStationText} numberOfLines={1} adjustsFontSizeToFit>· {item.linename}</Text>
                    </View>
                  )
                } else {
                  return (
                    <View style={stylesArrive.ChangeStationItem} key={index}>
                      <Image style={stylesArrive.ChangeStationImg} source={require('./public/assets/bg-arrive-border1.png')} />
                      <Text style={stylesArrive.ChangeStationText} numberOfLines={1} adjustsFontSizeToFit>· {item.linename}</Text>
                    </View>
                  )
                }
              }


            })}
          </View>
        </View>
      </ImageBackground>
      <Dialog visible={dialogVisible} onTouchOutside={() => setDialogVisible(false)}>
        <View style={style.fromBox}>
          <TextInput style={style.fromItem} value={carID_form} autoCapitalize={"characters"} autoComplete={"off"} autoCorrect={false} onChangeText={setCarIdForm} placeholder='请输入车辆编号' />
          <TextInput style={style.fromItem} value={ip_form} onChangeText={setIpForm} placeholder='请输入IP地址' />
          <TextInput style={style.fromItem} value={port_form} onChangeText={setProtForm} placeholder='请输入端口号' />
          
          {/* <View style={style.debugSwitch}>
            <Text style={style.debugSwitchText}>调试面板:</Text>
            <TouchableOpacity 
              style={[style.switchButton, debugVisible ? style.switchButtonActive : null]}
              onPress={() => setDebugVisible(!debugVisible)}
            >
              <Text style={[style.switchButtonText, debugVisible ? style.switchButtonTextActive : null]}>
                {debugVisible ? '开启' : '关闭'}
              </Text>
            </TouchableOpacity>
          </View> */}

          <View style={style.fromButton}>
            <View style={style.fromButtonItem}>
              <Button title='确认' onPress={setFrom} />
            </View>
            <View style={style.fromButtonItem}>
              <Button title='取消' onPress={() => setDialogVisible(false)} />
            </View>
          </View>
        </View>
      </Dialog>

      {/* <Modal visible={isLoading}>
        <View style={{ width: 100, height: 100,justifyContent: 'center', alignItems: 'center', borderRadius: 50}}>
          <Text style={{fontSize: 20,}}>{textLoading}</Text>
        </View>
      </Modal> */}
      <View style={[StylesSpecial.container, showCoverApp ? StylesSpecial.active : StylesSpecial.inActive]}>
        <Image style={StylesSpecial.img} source={{ uri: coverAppImg || undefined }} resizeMode='cover' />
      </View>

      {/* Debug面板 */}
      {/* <DebugPanel
        visible={debugVisible}
        onToggle={() => setDebugVisible(!debugVisible)}
        debugData={debugData}
        rawData={rawSocketData}
      /> */}
    </SafeAreaView>
  );
}

const stylesTitle = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: "row",
    justifyContent: 'center',
    alignItems: "center",
    gap: 10,
    marginTop: 10
  },
  title: {
    width: '68%',
    height: scaleSize(70),
    resizeMode: 'contain',
    // borderWidth: 1
    // backgroundColor: '#000'
    // alignSelf: 'flex-end'
  },
  config: {
    position: 'absolute',
    left: '85%'
  }
  // textContainer: {
  //   display: 'flex',
  //   flexDirection: "row",
  // },
  // text: {
  //   fontSize: 51 / 2
  // }

})
const stylesMain = StyleSheet.create({
  container: {
    borderRadius: 10,
    // borderStyle: 'solid',
    borderWidth: 1,
    borderColor: 'rgba(202, 216, 230, 1)',
    backgroundColor: 'rgba(255,255,255,0.7)',
    height: windowHeight * 0.6,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    overflow: 'hidden',
    gap: 10,
    marginTop: 10
    // opacity: 0.5
  },
  inActive: {
    display: 'none'
  }
})

const styles = StyleSheet.create({
  container: {
    resizeMode: 'stretch',
    backgroundColor: '#fff',
    width: windowWidth,
    height: windowHeight,
    padding: 16 / 2,
    paddingBottom: 30,
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    gap: 10 / 2,
  },
  containerBus: {
    backgroundColor: '#fff',
    // padding: 16/2,
    // paddingBottom: 30,
    height: windowHeight,
    // display: 'flex',
    // flexDirection: 'column',
    // gap: 10/2,
    // paddingBottom: 20,
    // overflow: 'hidden'
    // borderWidth: 1
  },
  shadomcontainer: {
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 2 },
    // shadowRadius: 10,
    // shadowOpacity: 0.1,
    // elevation: 2,
  },
  sectionContainer: {
    marginTop: 32,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  sectionDescription: {
    marginTop: 8,
    fontSize: 18,
    fontWeight: '400',
  },
  highlight: {
    fontWeight: '700',
  },
  wrapper: {
    width: '100%',
    backgroundColor: '#000',
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap'
  },
  slide1: {
    flex: 1,
    display: 'flex',
    width: windowWidth - 16,
    height: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff'
  },
  slide2: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff'
  },
  slide3: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff'
  },
  text: {
    color: '#fff',
    fontSize: 30,
    fontWeight: 'bold'
  }
});

const stylesMedia = StyleSheet.create({
  container: {
    // height: windowHeight * 0.25, 
    flex: 1,
    // backgroundColor: 'grey',
    borderRadius: 10,
    overflow: 'hidden',
    width: '100%'
    // borderWidth: 1
  },
  inActive: {
    display: 'none'
  },
  image: {
    width: '100%',
    height: '100%'
  },
  video: {
    width: '100%',
    height: '100%'
  }
})

const stylesArrive = StyleSheet.create({
  container: {
    display: 'none',
    zIndex: 999,
    width: windowWidth,
    height: windowHeight,
    position: 'absolute',
    left: 0,
    top: 0,
    gap: scaleSize(130)
  },
  bg: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    left: 0,
    top: 0
  },
  active: {
    display: 'flex',
    // gap: 10,
    // flex: 1,
    // // width: '100%',
    // // height: '100%',
    // flexDirection: 'column'
  },
  show: {
    display: 'flex'
  },
  hide: {
    display: 'none'
  },
  containerTop: {
    width: windowWidth * 0.9,
    height: windowWidth * 0.7,
    marginLeft: '5%',
    marginTop: '12%',
    transform: 'rotate(-5deg)'
  },
  tipsTop: {
    fontSize: scaleSize(50),
    fontWeight: 'bold',
    textAlign: 'center',
    // backgroundColor: '#000',
    width: '100%',
    color: "#fff",
    position: "absolute",
    top: '8.5%',
    transform: 'rotate(-1deg)'
  },
  stationContainer: {
    width: '70%',
    height: '60%',
    position: 'absolute',
    top: '30%',
    left: '15%',
    transform: 'rotate(-1deg)',
    display: 'flex',
    // flexDirection: 'row',
    gap: scaleSize(30),
    justifyContent: 'center',
    // justifyContent: 'center',
    // justifyContent: 'space-between'
    // backgroundColor: '#000'
  },
  mainTextContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between'
  },
  mainText: {
    fontSize: scaleSize(85),
    color: '#000',
    fontWeight: 'bold',
    textAlign: 'center'
    // textAlignVertical: 'center',
    // textAlign: 'left',
  },
  textCenter: {
    textAlign: 'center'
  },
  TopImg: {
    width: scaleSize(80),
    position: 'absolute',
    bottom: scaleSize(-80),
    left: '40%',
    // h
  },
  containerBottom: {
    width: windowWidth * 0.9,
    marginLeft: '5%',
    display: 'none'
  },
  tipsBottomContainer: {
    width: '100%',
    // height: scaleSize(100),
    // marginLeft: '10%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: scaleSize(-70),
    zIndex: 2
    // flex: 1
  },
  BottomImg: {
    width: '50%',
    // backgroundColor: '#fff',
    height: scaleSize(110),
    resizeMode: 'contain',
  },
  tipsBottom: {
    position: 'absolute',
    fontSize: scaleSize(50),
    lineHeight: scaleSize(50) * 1.3,
    letterSpacing: 2,
    fontWeight: 'bold',
    color: '#000'
  },
  ChangeStationContainer: {},
  ChangeStationItem: {
    width: '85%',
    // flex: 1,
    marginLeft: '2%',
    alignContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: -1,
    // height: scaleSize(200),
    // backgroundColor: '#000',
    // display: 'flex'
  },
  ChangeStationImg: {
    width: '100%',
    height: scaleSize(185),
    // flex: 1,
    resizeMode: 'contain',
    // backgroundColor: '#000'
  },
  ChangeStationText: {
    position: 'absolute',
    width: '80%',
    textAlign: 'left',
    fontWeight: 'bold',
    fontSize: scaleSize(60),
    color: '#000',
    letterSpacing: 2,
    height: scaleSize(185),
    lineHeight: scaleSize(200),
    // backgroundColor: '#000'
  }
})

const style = StyleSheet.create({
  body: { flex: 1 },
  buttons: {
    width: "100%",
    position: "absolute",
    flexDirection: "row",
    justifyContent: "center",
  },
  button: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    margin: 16,
    borderRadius: 50,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  },
  text: { fontSize: 16, color: "#000" },
  mapview: {
    position: "relative",
    height: "50%"
  },
  mapimg: {
    position: "absolute",
    left: 5,
    top: 5,
    width: windowWidth / 3.5,
    height: windowHeight / 5,
    resizeMode: "contain"
  },
  mapspeed: {
    position: "absolute",
    width: windowWidth / 3.5,
    left: 5,
    top: 5,
    textAlign: "center",
    fontSize: 24,
    fontWeight: "bold",
    color: '#000'
  },
  mapspeedaunit: {
    position: "absolute",
    width: windowWidth / 3.5,
    left: 5,
    top: 35,
    fontSize: 14,
    textAlign: "center"
  },
  mapTips: {
    position: "absolute",
    paddingTop: 5,
    paddingBottom: 3,
    width: windowWidth / 3.5,
    left: 10,
    top: 45,
    textAlign: "center",
    fontSize: 7,
    backgroundColor: "#FFF7F7",
    fontWeight: "600",
    color: '#000',
    borderRadius: 5,
    borderWidth: 1,
    borderColor: "#FF0000",
    borderStyle: "solid"
  },

  mapcar: {
    marginTop: 5,
    width: 28,
    height: 32,
    resizeMode: "cover"
  },

  markerText: {
    paddingTop: 3,
    paddingBottom: 3,
    paddingLeft: 8,
    paddingRight: 8,
    textAlign: "center",
    color: "#000",
    fontSize: 12,
    backgroundColor: "#F3F1FF",
    borderWidth: 1,
    borderRadius: 10,
    borderColor: "#7631FA",
    borderStyle: "solid"
  },
  markerBox: {
    display: "flex",
    alignItems: "center",
  },

  fromBox: {
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 10,
    paddingBottom: 10,
    display: "flex",
    width: windowWidth,
    height: windowHeight / 3
  },

  fromItem: {
    borderBottomColor: "#666",
    borderBottomWidth: 1,
    paddingBottom: 10,
    marginBottom: 10,
  },

  fromButton: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginTop: 20
  },

  fromButtonItem: {
    flex: 1,
    paddingRight: 10,
    paddingLeft: 10
  },

  debugSwitch: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingVertical: 10,
  },

  debugSwitchText: {
    fontSize: 16,
    color: '#333',
  },

  switchButton: {
    backgroundColor: '#ccc',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 15,
    minWidth: 60,
    alignItems: 'center',
  },

  switchButtonActive: {
    backgroundColor: '#007AFF',
  },

  switchButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
  },

  switchButtonTextActive: {
    color: '#fff',
  },

  notification: {
    display: "flex",
    alignItems: "flex-end",
    position: "absolute",
    top: 10,
    right: 10,
    width: windowWidth / 2.8,
    height: 300
  },

  notificationItem: {
    marginBottom: 10,
    paddingLeft: 8,
    paddingRight: 8,
    paddingTop: 4,
    paddingBottom: 4,
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#FF0000',
    backgroundColor: "rgba(255, 247, 247, 0.9)"
  },

  notificationItemText: {
    color: "#000",
    fontSize: 12,
    letterSpacing: 0.5,
    fontWeight: "600"
  }
});

const StylesSpecial = StyleSheet.create({
  container: {
    width: windowWidth,
    height: windowHeight,
    position: 'absolute',
    left: 0,
    top: 0,
    backgroundColor: "#fff",
    zIndex: 99
  },
  active: {
    display: 'flex'
  },
  inActive: {
    display: 'none'
  },
  img: {
    width: '100%',
    height: '100%'
  }
})


export default App;
