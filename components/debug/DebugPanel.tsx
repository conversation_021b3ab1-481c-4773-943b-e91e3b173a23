import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { DebugPanelProps } from '../../types';

const DebugPanel: React.FC<DebugPanelProps> = ({ visible, coordinates }) => {
  if (!visible) {
    return null;
  }

  const formatCoordinate = (value: number | null): string => {
    if (value === null || value === undefined) {
      return '无数据';
    }
    return value.toFixed(7);
  };

  const formatTimestamp = (timestamp: string): string => {
    return timestamp || '无时间戳';
  };

  const formatRawData = (raw: any): string => {
    if (!raw) {
      return '无原始数据';
    }
    try {
      return JSON.stringify(raw, null, 2);
    } catch (error) {
      return '数据格式错误';
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>DEBUG INFO</Text>
      <Text style={styles.coordinate}>
        纬度: {formatCoordinate(coordinates.latitude)}
      </Text>
      <Text style={styles.coordinate}>
        经度: {formatCoordinate(coordinates.longitude)}
      </Text>
      <Text style={styles.timestamp}>
        时间: {formatTimestamp(coordinates.timestamp)}
      </Text>
      <Text style={styles.rawTitle}>原始数据:</Text>
      <Text style={styles.rawData} numberOfLines={5}>
        {formatRawData(coordinates.raw)}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 10,
    borderRadius: 5,
    minWidth: 250,
    maxWidth: 300,
    zIndex: 1000,
  },
  title: {
    color: '#00ff00',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  coordinate: {
    color: '#ffffff',
    fontSize: 12,
    marginBottom: 2,
  },
  timestamp: {
    color: '#ffffff',
    fontSize: 12,
    marginBottom: 5,
  },
  rawTitle: {
    color: '#00ff00',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  rawData: {
    color: '#cccccc',
    fontSize: 10,
    fontFamily: 'monospace',
  },
});

export default DebugPanel;