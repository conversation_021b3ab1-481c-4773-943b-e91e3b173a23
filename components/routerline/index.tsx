import React, { useState, useRef, useEffect } from 'react'
import type { FC } from 'react'
import {
    StyleSheet,
    Text,
    View,
    Easing,
    Dimensions,
    ImageBackground
  } from 'react-native';
import TextTicker from 'react-native-text-ticker'
// import Paragraph from 'react-native-rich-text'
// import {StyleSheet} from '../styleSheet'
// import Icon from 'react-native-vector-icons'
import { Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
// import Icon from 'react-native-vector-icons/FontAwesome'
const Icon_bus  = require('../../public/assets/icon-bus.png')
const Icon_direct  = require('../../public/assets/icon-direct.png')

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;
const baseWidth = 1080

// 自适应字体函数
const scaleSize = (size: number) => {
  // 计算缩放比例
  const scale = windowWidth / baseWidth;
  // 返回缩放后的字体大小
  return size * scale;
};

interface RouterLineProps {
  height: number;
  listStation: any[];
  currentStationIndex: number;
  infoLine: {
    lineName: string;
    FirstTime: string;
    EndTime: string;
    price: string;
    startStationNameActually: string;
    endStationNameActually: string;
    fcsj: string;
  };
  dataNextStation: {
    arrival_distance: number | null;
  };
  nextStationName: string;
  dataChangeStation: any[];
}

const RouterLine: FC<RouterLineProps> = (props) => {
    const textscroll = useRef<any>(null)
    const textscrollChangeStation = useRef<any>(null)
    // const scrollViewRef = useRef(null);
    // const scrollItemRef = useRef(null);
    const [textListChangeStation, setTextListChangeStation] = useState<string>('')
    const [listStationShow, setListStationShow] = useState<any[]>([])
    // const [tChangeStation, setTChangeStation] = useState(null)
    // const [isArrive, setIsArrive] = useState(false)
    
    // const [testList, setTestList] = useState([
    //     {
    //         status: 'status1',
    //         stationName: '环湖西路',
    //         listStationName: ['环西湖三路']
    //     },
    //     {
    //         status: 'status2',
    //         stationName: '环湖西路2',
    //         listStationName: ['环西湖三路环西湖三路','临港大道']
    //     },
    //     {
    //         status: 'status3',
    //         stationName: '环湖西路3',
    //         listStationName: ['环西湖三路','临港大道']
    //     }
    // ])
    
    
    // useEffect(() => {
    //     init();
    // }, [props.currentStationIndex])
    // 当前站点信息处理
    const isAtFinalStation = () => {
        return props.currentStationIndex >= props.listStation.length - 1
        // return true;
    }

    const handleCurrentStation = () => {
        let index_current = props.currentStationIndex
        const length_station = props.listStation.length
        console.log('index: ' + index_current,length_station)
        
        if (index_current + 2 <= length_station - 1) {
            const listStationShow = props.listStation.slice(index_current, index_current + 3)
            listStationShow.map((item,index) => {
                if (index == 0) {
                    item.status = 'status1'
                } else if (index == 1) {
                    item.status = 'status2'
                } else if (index == 2) {
                    item.status = 'status3'
                }
                if (item.stationName2?.length == 0) {
                    item.listStationName = [item.stationName]
                } else {
                    item.listStationName = item.stationName2
                }
                return item
            })
            // console.log(listStationShow)
            setListStationShow(listStationShow)
        } else {
            const listStationShow = props.listStation.slice(-3)
            console.log(listStationShow)
            const listStationId2 = listStationShow.map(item => {
                return item.id
            })
            const currentId = props.listStation[index_current]?.id
            const index_current2 = listStationId2.indexOf(currentId)

            // console.log(listStationId2,currentId,index_current2,)
            listStationShow.map((item,index) => {
                if (index_current2 >=  index) {
                    if (isAtFinalStation() && index === listStationShow.length - 1) {
                        item.status = 'final_station'
                    } else {
                        item.status = 'status1'
                    }
                } else {
                    item.status = 'status2'
                }
                if (item.stationName2?.length == 0) {
                    item.listStationName = [item.stationName]
                } else {
                    item.listStationName = item.stationName2
                }
                return item
            })
            
            setListStationShow(listStationShow)
        }
    }
    useEffect(() => {
        if(props.listStation.length) {
            console.log('站点发生变化',props.currentStationIndex)
            handleCurrentStation()
        }
    }, [props.infoLine?.fcsj, props.currentStationIndex])
    useEffect(() => {
        handleScrollStations()
    }, [JSON.stringify(props.dataChangeStation)])
    const handleScrollStations = () => {
        // if (textscroll.current) {
        //     textscroll.current.stopAnimation()
        // }
        let result = ''
        // console.log(222, props.dataChangeStation)
        props.dataChangeStation.forEach((item) => {
            result =  result == '' ? item.linename : `${result} ${item.linename}`
        })
        if (textListChangeStation != result) {
            setTextListChangeStation(result)
        }
        
        // if (textscroll.current) {
        //     textscroll.current.startAnimation()
        // }
        if (textscrollChangeStation.current) {
            textscrollChangeStation.current.stopAnimation()
            
        }
        if (textscrollChangeStation.current) {
            textscrollChangeStation.current.startAnimation()
        }
        
    }
    return (
        <View style={{height: props.height}}>
            <ImageBackground style={styles.container} resizeMode='contain' imageStyle={styles.backStyle} source={require('../../public/assets/bg-station.png')}>
                <View style={stylesInfoSimple.container}>
                    <View style={stylesInfoSimple.nameLine}>
                        <Image source={Icon_bus} style={stylesInfoSimple.iconBus} />
                        <Text style={stylesInfoSimple.titleLine}>{props.infoLine.lineName}</Text>
                    </View>
                    
                    <View style={stylesInfoSimple.infoTime}>
                        <View style={stylesInfoSimple.containerTime}>
                            <LinearGradient
                                style={stylesInfoSimple.gradientContainer}
                                colors={['#7EB1FF', '#345CFF']}
                                start={{ x: 0, y: 0.5 }}
                                end={{ x: 1, y: 0.5 }}
                            >
                                <Text style={stylesInfoSimple.iconTetxTime}>首</Text>
                            </LinearGradient>
                            <Text style={stylesInfoSimple.textTime}>{props.infoLine.FirstTime}</Text>
                        </View>
                        <View style={stylesInfoSimple.containerTime}>
                            <LinearGradient
                                style={stylesInfoSimple.gradientContainer}
                                colors={['#7EB1FF', '#345CFF']}
                                start={{ x: 0, y: 0.5 }}
                                end={{ x: 1, y: 0.5 }}
                            >
                                <Text style={stylesInfoSimple.iconTetxTime}>末</Text>
                            </LinearGradient>
                            <Text style={stylesInfoSimple.textTime}>{props.infoLine.EndTime}</Text>
                        </View>
                        <View style={stylesInfoSimple.containerTime}>
                            <LinearGradient
                                style={stylesInfoSimple.gradientContainer}
                                colors={['#7EB1FF', '#345CFF']}
                                start={{ x: 0, y: 0.5 }}
                                end={{ x: 1, y: 0.5 }}
                            >
                                <Text style={stylesInfoSimple.iconTetxTime}>票</Text>
                            </LinearGradient>
                            <Text style={stylesInfoSimple.textTime}>{props.infoLine.price}</Text>
                        </View>
                    </View>
                    <View style={stylesInfoSimple.startEndLine}>
                        <Text style={stylesInfoSimple.titleStartEnd}>{props.infoLine.startStationNameActually}</Text>
                        <Image source={Icon_direct} style={stylesInfoSimple.IconDirect} />
                        <Text style={stylesInfoSimple.titleStartEnd}>{props.infoLine.endStationNameActually}</Text>
                    </View>
                </View>
                {isAtFinalStation() ? (
                    <View style={[stylesTips.container, stylesTips.finalStationContainer]}>
                        <Image source={require('../../public/assets/icon-bus-2.png')} style={stylesTips.icon} />
                        <Text style={[stylesTips.text, stylesTips.finalStationText]}>到达终点</Text>
                    </View>
                ) : (
                    <View style={stylesTips.container}>
                        <Image source={require('../../public/assets/icon-bus-2.png')} style={stylesTips.icon} />
                        <Text style={stylesTips.text}>前方到站{props.dataNextStation.arrival_distance != null ? props.dataNextStation.arrival_distance + '米' : ''}</Text>
                    </View>
                )}
                <View style={stylesLine.container}>
                    <View style={stylesLine.lines}>
                        {listStationShow.map((item, index) => {
                            return (
                                <View key={item.stationName} style={[stylesLine.station]}>
                                    <View style={[
                                        item.status == 'status1' ? stylesLine.point
                                        : item.status == 'status2' ? stylesLine.point2
                                        : item.status == 'status3' ? stylesLine.point3 
                                        : item.status == 'final_station' ? stylesLine.finalStationPoint : undefined]} >
                                    </View>
                                    
                                    <View style={stylesLine.stationNameList}>
                                        {item.listStationName?.map((item2: string, index2: number) => {
                                            if (item.listStationName.length == 1) {
                                                return (
                                                    <Text 
                                                    style={[
                                                        item.status == 'status1' ? stylesLine.stationName
                                        : item.status == 'status2' ? stylesLine.stationName2
                                        : item.status == 'status3' ? stylesLine.stationName3 
                                        : item.status == 'final_station' ? stylesLine.finalStationName : undefined ]} 
                                                        key={item2}>
                                                        {item2}
                                                    </Text>
                                                )
                                            } else {
                                                return (
                                                    <Text adjustsFontSizeToFit 
                                                    style={[
                                                        item.status == 'status1' ? stylesLine.stationName
                                        : item.status == 'status2' ? stylesLine.stationName2
                                        : item.status == 'status3' ? stylesLine.stationName3 
                                        : item.status == 'final_station' ? stylesLine.finalStationName : undefined ]} 
                                                        key={item2} 
                                                        numberOfLines={1}>
                                                        {item2}
                                                    </Text>
                                                )
                                            }
                                            
                                        })}
                                    </View>
                                </View>
                            )
                        })
                        }
                        {/* {listStationShow.map((item, index) => (
                            <View key={item.stationName} style={[stylesLine.station]}>
                                <View style={[
                                    item.status == 'status1' ? stylesLine.point
                                    : item.status == 'status2' ? stylesLine.point2
                                    : item.status == 'status3' ? stylesLine.point3 : undefined ]} ></View>
                                <Text style={[
                                    item.status == 'status1' ? stylesLine.stationName
                                    : item.status == 'status2' ? stylesLine.stationName2
                                    : item.status == 'status3' ? stylesLine.stationName3 : undefined ]} numberOfLines={2}>{item.stationName}</Text>
                            </View>
                        ))} */}
                    </View>
                </View>
                <View style={stylesInfoNextStation.container}>
                    <View style={stylesInfoNextStation.nextStation}>
                        <View style={stylesInfoNextStation.iconNotificationContainer}>
                            <Image source={require('../../public/assets/icon-Notification.png')} style={stylesInfoNextStation.iconNotification} />
                        </View>
                        <View style={stylesInfoNextStation.notificationInfo}>
                            <TextTicker 
                                ref={textscroll}
                                duration={(props.nextStationName.length) * 400}
                                // scrollSpeed= { 300 }
                                loop 
                                // bounce 
                                repeatSpacer={10} 
                                // marqueeDelay={1000} 
                                easing={Easing.linear}
                                style={stylesInfoNextStation.notificationStation}
                            >
                                {props.nextStationName}
                                <Text style={stylesInfoNextStation.notificationTips}>  即将到站</Text>
                            </TextTicker>
                        </View>
                        
                    </View>
                    <View style={stylesInfoNextStation.separate}></View>
                    <View style={stylesInfoNextStation.changeStation}>
                        <Text style={stylesInfoNextStation.changeTips}>可换乘</Text>
                        <Image source={require('../../public/assets/icon-link.png')} style={stylesInfoNextStation.iconLink} />
                        <View style={stylesInfoNextStation.listChangeStation}>
                            <TextTicker 
                                ref={textscrollChangeStation}
                                duration={textListChangeStation.length * 150}
                                marqueeOnMount={false} 
                                loop 
                                marqueeDelay={0}
                                // bounce 
                                repeatSpacer={50} 
                                // marqueeDelay={1000} 
                                easing={Easing.linear}
                                style={[stylesInfoNextStation.changebus,stylesInfoNextStation.cloestBus]}
                            >
                                {textListChangeStation}
                            </TextTicker>
                        </View>
                    </View>
                </View>
            </ImageBackground>
            {/* <View style={{ position: 'relative', height: '100%' }}>
                <View style={stylesInfoSimple.container}>
                    <View style={stylesInfoSimple.nameLine}>
                        <Image source={Icon_bus} style={stylesInfoSimple.iconBus} />
                        <Text style={stylesInfoSimple.titleLine}>{props.infoLine.lineName}</Text>
                    </View>
                    
                    <View style={stylesInfoSimple.infoTime}>
                        <View style={stylesInfoSimple.containerTime}>
                            <LinearGradient
                                style={stylesInfoSimple.gradientContainer}
                                colors={['#7EB1FF', '#345CFF']}
                                start={{ x: 0, y: 0.5 }}
                                end={{ x: 1, y: 0.5 }}
                            >
                                <Text style={stylesInfoSimple.iconTetxTime}>首</Text>
                            </LinearGradient>
                            <Text style={stylesInfoSimple.textTime}>{props.infoLine.FirstTime}</Text>
                        </View>
                        <View style={stylesInfoSimple.containerTime}>
                            <LinearGradient
                                style={stylesInfoSimple.gradientContainer}
                                colors={['#7EB1FF', '#345CFF']}
                                start={{ x: 0, y: 0.5 }}
                                end={{ x: 1, y: 0.5 }}
                            >
                                <Text style={stylesInfoSimple.iconTetxTime}>末</Text>
                            </LinearGradient>
                            <Text style={stylesInfoSimple.textTime}>{props.infoLine.EndTime}</Text>
                        </View>
                        <View style={stylesInfoSimple.containerTime}>
                            <LinearGradient
                                style={stylesInfoSimple.gradientContainer}
                                colors={['#7EB1FF', '#345CFF']}
                                start={{ x: 0, y: 0.5 }}
                                end={{ x: 1, y: 0.5 }}
                            >
                                <Text style={stylesInfoSimple.iconTetxTime}>票</Text>
                            </LinearGradient>
                            <Text style={stylesInfoSimple.textTime}>{props.infoLine.price}</Text>
                        </View>
                    </View>
                    <View style={stylesInfoSimple.startEndLine}>
                        <Text style={stylesInfoSimple.titleStartEnd}>{props.infoLine.qdzName}</Text>
                        <Image source={Icon_direct} style={stylesInfoSimple.IconDirect} />
                        <Text style={stylesInfoSimple.titleStartEnd}>{props.infoLine.zdzName}</Text>
                    </View>
                </View>
                <View style={stylesTips.container}>
                    <Image source={require('../../public/assets/icon-bus-2.png')} style={stylesTips.icon} />
                    <Text style={stylesTips.text}>前方到站{props.dataNextStation.arrival_distance != null ? props.dataNextStation.arrival_distance + '米' : ''}</Text>
                </View>
                <View style={stylesLine.container}>
                    <View style={stylesLine.lines}>
                        {listStationShow.map((item, index) => (
                            <View key={item.stationName} style={[stylesLine.station]}>
                                <View style={[
                                    item.status == 'status1' ? stylesLine.point
                                    : item.status == 'status2' ? stylesLine.point2
                                    : item.status == 'status3' ? stylesLine.point3 : undefined ]} ></View>
                                <Text style={[
                                    item.status == 'status1' ? stylesLine.stationName
                                    : item.status == 'status2' ? stylesLine.stationName2
                                    : item.status == 'status3' ? stylesLine.stationName3 : undefined ]} numberOfLines={2}>{item.stationName}</Text>
                            </View>
                        ))}
                    </View>
                </View>
                <View style={stylesInfoNextStation.container}>
                    <View style={stylesInfoNextStation.nextStation}>
                        <View style={stylesInfoNextStation.iconNotificationContainer}>
                            <Image source={require('../../public/assets/icon-Notification.png')} style={stylesInfoNextStation.iconNotification} />
                        </View>
                        <View style={stylesInfoNextStation.notificationInfo}>
                            <TextTicker 
                                ref={textscroll}
                                duration={(props.dataNextStation.station_name.length) * 400}
                                // scrollSpeed= { 300 }
                                loop 
                                bounce 
                                repeatSpacer={10} 
                                // marqueeDelay={1000} 
                                easing={Easing.linear}
                                style={stylesInfoNextStation.notificationStation}
                            >
                                {props.dataNextStation.station_name}
                                <Text style={stylesInfoNextStation.notificationTips}>  即将到站</Text>
                            </TextTicker>
                        </View>
                        
                    </View>
                    <View style={stylesInfoNextStation.separate}></View>
                    <View style={stylesInfoNextStation.changeStation}>
                        <Text style={stylesInfoNextStation.changeTips}>可换乘</Text>
                        <Image source={require('../../public/assets/icon-link.png')} style={stylesInfoNextStation.iconLink} />
                        <View style={stylesInfoNextStation.listChangeStation}>
                            <TextTicker 
                                ref={textscrollChangeStation}
                                duration={textListChangeStation.length * 150}
                                marqueeOnMount={false} 
                                loop 
                                bounce 
                                repeatSpacer={50} 
                                // marqueeDelay={1000} 
                                easing={Easing.linear}
                                style={[stylesInfoNextStation.changebus,stylesInfoNextStation.cloestBus]}
                            >
                                {textListChangeStation}
                            </TextTicker>
                        </View>
                    </View>
                </View>
            </View> */}
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        position: 'relative', 
        height: '100%',
        // resizeMode: 'contain',
    },
    backStyle: {
        width: '98%',
        left: '1%',
        top: scaleSize(180),

        // bottom: -100
    },
    infoLine: {
        // display: 'flex',
        // justifyContent: 'space-between',
        // flexDirection: 'row',
        // padding: 5,
        // height: height
    },
   
})

const stylesInRoad = StyleSheet.create({
    inActive: {
        display: 'none'
    }
})

const stylesInStation = StyleSheet.create({
    inActive: {
        display: 'none'
    },
    container: {
        display: 'flex',
        flexDirection: 'row',
        gap: 10,
        flexWrap: 'nowrap',
        width: '98%',
        // marginLeft: 5,
        alignSelf: 'center',
        // alignContent: 'center',
        alignItems: 'center',
        // marginTop: 10,
        // backgroundColor: 'grey',
        height: '100%',
        // borderColor: 'rgba(155, 150, 100, 1)',
        // borderWidth: 1,
        borderRadius: 10,
        overflow: 'hidden'
    },
    containerLeft: {
        width: '60%',
        height: '90%',
        overflow: 'hidden',
        borderWidth: 1,
        borderRadius: 10,
        borderColor: 'rgba(71, 202, 108, 1)',
        backgroundColor: 'rgba(71, 202, 108, 1)',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
    },
    containerRight: {
        width: '35%',
        height: '90%',
        borderRadius: 10,
        borderWidth: 1,
        borderColor: 'rgba(138, 138, 138, 1)',
        paddingHorizontal: 10,
        paddingVertical: 5
    },
    currentStation: {
        fontSize: 76.6/2,
        lineHeight: (76.6/2) * 1.2,
        color: "#fff",
        fontWeight: 'bold',
        textAlign:'center',
        paddingHorizontal: 5
    },
    currentStation2: {
        fontSize: 36.67/2,
        color: "#fff",
        fontWeight: 'bold',
        textAlign:'center'
    },
    tips: {
        fontSize: 73/2,
        color: "#fff",
        fontWeight: 'bold',
        textAlign:'center'
    },
    textChangeStation: {
        fontSize: 18/2,
        fontWeight: 'bold',
        color: "#000"
    },
    listChangeStation: {
        // borderWidth: 1,
        paddingVertical: 5,
        marginTop: 10
        // display: 'flex',
        // flexDirection: 'column',
        // gap: 10
    },
    listChangeItem: {
        display: 'flex',
        flexDirection: 'row',
        gap: 10,
        alignItems: 'center',
        marginBottom: 15,
        borderWidth: 1,
        borderColor: 'transparent'
    },
    stationPoint: {
        width: 15/2,
        height: 15/2,
        borderRadius: 50,
        borderWidth: 1,
        borderColor: 'rgba(0, 200, 155, 1)',
        backgroundColor: '#fff'
    },
    stationName: {
        backgroundColor: 'rgba(0, 200, 155, 1)',
        borderRadius: 10,
        fontSize: 18/2,
        color: '#fff',
        paddingHorizontal: 5,
        paddingVertical: 2,
        flex: 1
    },
    line: {
        position: 'absolute',
        left: 3,
        top: 5,
        width: 1,
        height: '200%',
        // paddingTop: 50,
        backgroundColor: 'rgba(0, 200, 155, 1)'
    }
})

const stylesInfoSimple = StyleSheet.create({
    container: {
        display: 'flex',
        justifyContent: 'space-between',
        flexDirection: 'row',
        padding: 5,
        alignItems: 'center',
        flexWrap: 'wrap'
    },
    
    nameLine: {
        display: 'flex',
        justifyContent: 'space-between',
        flexDirection: 'row',
        // flexDirection: 'column',
        alignContent: 'center',
        alignSelf: 'center',
        gap: 5,
        
        // backgroundColor: 'grey'
    },
    iconBus: {
        width: scaleSize(32),
        height: scaleSize(30),
        alignSelf: 'center',
    },
    titleLine: {
        color: '#000',
        fontSize: scaleSize(40),
        lineHeight: scaleSize(40) * 1.3,
        fontWeight: '500'
        // marginTop: -2
        // borderWidth: 1,
        // borderStyle: 'solid'
        // lineHeight: 26.67/2 * 1.3,
        // display: 'flex',
        // flexDirection: 'column',
        // justifyContent: 'center',
        // alignSelf: 'center',
    },
    startEndLine: {
        display: 'flex',
        justifyContent: 'center',
        flexDirection: 'row',
        gap: 5,
        verticalAlign: 'middle',
        width: '100%',
        marginTop: 10
    },
    titleStartEnd: {
        color: '#000',
        fontSize: scaleSize(27),
        alignSelf: 'center'
    },
    IconDirect: {
        width: scaleSize(27.48),
        height: scaleSize(9.35),
        alignSelf: 'center'
    },
    infoTime: {
        // height: 35/2,
        // padding: 5,
        // backgroundColor: '#DEECFF',
        
        // borderRadius: 5,
        display: 'flex',
        flexDirection: 'row',
        gap: 5
    },
    gradientContainer: {
        width: scaleSize(40),
        height: scaleSize(40),
        // padding: 2,
        borderRadius: 2,
    },
    containerTime: {
        display: 'flex',
        flexDirection: 'row',
        gap: 2,
        
        alignSelf: 'center'

    },
    iconTetxTime: {
        color: "#fff",
        fontSize: scaleSize(30),
        lineHeight: scaleSize(30) * 1.3,
        textAlign: 'center'
        // borderColor: "#000",
        // textAlign: 'center',
        // alignSelf: 'center'
        // backgroundColor: "#fff"
        
        // backgroundColor: 'linear-gradient(90deg, #7EB1FF 0%, #345CFF 100%)',
    },
    // timeGradientContainer: {
    //     backgroundColor: 'linear-gradient(90deg, #7EB1FF 0%, #345CFF 100%)',
    // }
    textTime: {
        color: '#000',
        fontSize: scaleSize(27),
        lineHeight: scaleSize(27) * 1.4,
        // alignContent: 'center',
        // alignItems: 'center',
        // alignSelf: 'center',
    }
})

const stylesLine = StyleSheet.create({
    container: {
        paddingTop: 15,
        // display: 'none'
    },
    lines: {
        width: "98%",
        marginLeft: '1%',
        height: scaleSize(23),
        backgroundColor: 'rgba(77, 122, 255, 1)',
        borderTopLeftRadius: 50,
        borderBottomLeftRadius: 50,
        borderTopRightRadius: 100,
        borderBottomRightRadius: 0,
        display: 'flex',
        paddingLeft: '15%',
        paddingRight: '15%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        flexWrap: 'nowrap',
        alignItems: 'center',
        overflow: 'visible',
    },
    station: {
        position: 'relative',
        overflow: 'visible',
        // display: 'flex',
        flexDirection: 'column',
        // flexDirection: 'row',
        alignItems: 'center',
        width: scaleSize(45),
        height: scaleSize(45),
        // borderWidth: 1
    },
    point: {
        width: scaleSize(45),
        height: scaleSize(45),
        borderRadius: 50,
        backgroundColor: "rgba(128, 128, 128, 1)",
        borderWidth: 1,
        borderStyle: 'solid',
        borderColor: '#fff'
    },
    point2: {
        width: scaleSize(45),
        height: scaleSize(45),
        borderRadius: 50,
        backgroundColor: "#FF6700",
        borderWidth: 1,
        borderStyle: 'solid',
        borderColor: '#fff'
    },
    point3: {
        width: scaleSize(45),
        height: scaleSize(45),
        borderRadius: 50,
        backgroundColor: "#4981FF",
        borderWidth: 1,
        borderStyle: 'solid',
        borderColor: '#fff'
    },
    stationNameList: {
        position: 'absolute',
        width: scaleSize(330),
        top: scaleSize(60),
        // borderWidth: 1
    },
    stationName: {
        color: 'rgba(128, 128, 128, 1)',
        fontSize: scaleSize(40),
        // fontWeight: 'bold',
        lineHeight: scaleSize(40) * 1.3,
        // position: 'absolute',
        // width: scaleSize(300),
        textAlign: 'center',
        // top: 20,
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        overflow: 'visible',
    },
    stationName2: {
        color: '#FF6700',
        fontSize: scaleSize(40),
        fontWeight: 'bold',
        lineHeight: scaleSize(40) * 1.3,
        // paddingTop: scaleSize(30),
        // position: 'absolute',
        // width: scaleSize(300),
        // borderWidth: 1,
        textAlign: 'center',
        // top: 20,
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        overflow: 'visible',
    },
    stationName3: {
        color: '#000',
        fontSize: scaleSize(40),
        // fontWeight: 'bold',
        lineHeight: scaleSize(40) * 1.3,
        // paddingTop: scaleSize(30),
        // position: 'absolute',
        // width: scaleSize(300),
        // borderWidth: 1,
        textAlign: 'center',
        // top: 20,
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        overflow: 'visible',
    },
    duohangStationName: {
        width: 26.67*3,
    },
    
    inActiveStationName: {
        color: 'rgba(125, 160, 205, 1)',
    },
    activeStationName: {
        color: 'rgba(253, 140, 74, 1)',
        borderColor: 'rgba(253, 140, 74, 1)',
        borderStyle: 'dotted',
        borderWidth: 1,
        borderRadius: 10
    },
    activePoint: {
        backgroundColor: 'rgba(255, 133, 0, 1)'
    },
    finalStationPoint: {
        width: scaleSize(45),
        height: scaleSize(45),
        borderRadius: 50,
        // backgroundColor: "#FF6700",
        backgroundColor: "#4981FF",
        borderWidth: 1,
        borderStyle: 'solid',
        // borderColor: '#FFD700'
    },
    finalStationName: {
        color: '#FF6700',
        fontSize: scaleSize(40),
        fontWeight: 'bold',
        lineHeight: scaleSize(40) * 1.3,
        textAlign: 'center',
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        overflow: 'visible',
        // textShadowColor: '#FFD700',
        // textShadowOffset: { width: 1, height: 1 },
        // textShadowRadius: 2
    }
})

const stylesTips = StyleSheet.create({
    container: {
        display: 'flex',
        flexDirection: 'row',
        gap: 5,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 2
    },
    finalStationContainer: {
        justifyContent: 'flex-end',
        paddingRight: scaleSize(50)
    },
    icon: {
        width: scaleSize(150),
        height: scaleSize(72),
        alignSelf: 'center'
    },
    text: {
        fontSize: scaleSize(40),
        lineHeight: scaleSize(40) * 1.3,
        color: '#fff',
        letterSpacing: 0.07,
        paddingLeft: 15/2,
        paddingRight: 15/2,
        paddingTop: 3/2,
        paddingBottom: 3/2,
        backgroundColor: 'rgba(85, 128, 255, 1)',
        borderRadius: 5,
        alignSelf: 'center'
    },
    finalStationText: {
        backgroundColor: 'rgba(85, 128, 255, 1)',
        fontWeight: 'bold'
    }
})

const stylesInfoNextStation = StyleSheet.create({
    container: {
        width: '98%',
        marginLeft: '1%',
        display: 'flex',
        flexDirection: 'row',
        backgroundColor: '#fff',
        alignItems: 'center',
        // backgroundColor: 'grey',
        elevation: 5,
        borderRadius: 2,
        overflow: 'hidden',
        position: 'absolute',
        bottom: 0,
        // marginTop: 70,
    },
    nextStation: {
        width: '35%',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        overflow: 'hidden'
    },
    changeStation: {
        width: '64%',
        paddingLeft: 5,
        // paddingRight: 5,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        // justifyContent: 'space-between',
        gap: 5,
    },
    iconNotificationContainer: {
        width: scaleSize(62),
        height: scaleSize(62),
        backgroundColor: 'rgba(77, 122, 255, 1)',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center'
    },
    iconNotification: {
        width: scaleSize(36),
        height: scaleSize(36),
    },
    notificationInfo: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        justifyContent: 'space-between',
        
        flex: 1,
        gap: 10,
        paddingLeft: 5,
        paddingRight: 5,
        // overflow: 'hidden'
    },
    notificationStation: {
        fontSize: scaleSize(30),
        color: "#000",
        borderWidth: 1,
        borderColor: '#fff',
        // width: '20%'
    },
    notificationTips: {
        fontSize: scaleSize(30),
        color: "rgba(255, 103, 0, 1)",
        // paddingLeft: 5
    },
    separate: {
        width: 1,
        height: 28/2,
        backgroundColor: 'rgba(216, 216, 216, 1)'
    },
    changeTips: {
        fontSize: scaleSize(30),
        fontWeight: '400',
        color: '#000',
        borderWidth: 1,
        borderColor: '#fff'
    },
    iconLink: {
        width:  scaleSize(26),
        height: scaleSize(26),
        // marginLeft: 2,
        alignSelf: 'center'
    },
    listChangeStation: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        gap: 5,
        flex: 1,
        // width: 150
    },
    contentContainer: {
        height: '100%', // 滚动内容的宽度
      },
    changebus: {
        fontSize: scaleSize(30),
        color: 'rgba(54, 126, 255, 1)'
    },
    cloestBus: {}
})



export default React.memo(RouterLine)