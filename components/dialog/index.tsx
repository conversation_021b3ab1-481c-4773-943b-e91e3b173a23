import * as React from "react";
import { Modal, ModalContent } from 'react-native-modals';

export interface DialogProps {
  visible: boolean;
  children: React.ReactNode;
  onTouchOutside: () => void;
  // onConfirm: () => void;
  // onCancel: () => void;
}

export default class extends React.PureComponent<DialogProps> {
  static defaultProps = { visible: false };

  render () {
    return (
      <Modal
        visible={this.props.visible}
        onTouchOutside={this.props.onTouchOutside}
      >
        <ModalContent>
          { this.props.children }
        </ModalContent>
      </Modal>
    )
  }
}
