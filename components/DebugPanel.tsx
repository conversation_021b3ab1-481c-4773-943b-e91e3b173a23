import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  PanResponder,
  Animated,
  StyleSheet,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import { DebugCoordinates } from '../types';

interface DebugPanelProps {
  visible: boolean;
  onToggle: () => void;
  debugData: DebugCoordinates | null;
  rawData: any;
}

const DebugPanel: React.FC<DebugPanelProps> = ({
  visible,
  onToggle,
  debugData,
  rawData,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [position, setPosition] = useState({ x: 20, y: 100 });
  const pan = useRef(new Animated.ValueXY({ x: position.x, y: position.y })).current;

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      pan.setOffset({
        x: position.x,
        y: position.y,
      });
    },
    onPanResponderMove: Animated.event(
      [null, { dx: pan.x, dy: pan.y }],
      { useNativeDriver: false }
    ),
    onPanResponderRelease: (evt, gestureState) => {
      const newX = position.x + gestureState.dx;
      const newY = position.y + gestureState.dy;
      setPosition({ x: newX, y: newY });
      pan.flattenOffset();
    },
  });

  const copyToClipboard = (text: string) => {
    // 复制功能暂时禁用，可以在后续添加clipboard库支持
    console.log('复制内容:', text);
  };

  const formatJson = (obj: any) => {
    return JSON.stringify(obj, null, 2);
  };

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ translateX: pan.x }, { translateY: pan.y }],
          height: expanded ? 400 : 120,
        },
      ]}
      {...panResponder.panHandlers}
    >
      {/* 标题栏 */}
      <View style={styles.header}>
        <View style={styles.dragHandle} />
        <Text style={styles.title}>GPS调试面板</Text>
        <TouchableOpacity onPress={() => setExpanded(!expanded)}>
          <Icon 
            name={expanded ? "chevron-up" : "chevron-down"} 
            size={16} 
            color="#fff" 
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={onToggle} style={styles.closeButton}>
          <Icon name="times" size={16} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* 基础信息 */}
      <View style={styles.basicInfo}>
        <Text style={styles.infoText}>
          纬度: {debugData?.latitude?.toFixed(7) || 'N/A'}
        </Text>
        <Text style={styles.infoText}>
          经度: {debugData?.longitude?.toFixed(7) || 'N/A'}
        </Text>
        <Text style={styles.infoText}>
          时间: {debugData?.timestamp || 'N/A'}
        </Text>
      </View>

      {/* 详细信息 */}
      {expanded && (
        <ScrollView style={styles.detailSection}>
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>原始坐标数据</Text>
              <TouchableOpacity 
                onPress={() => copyToClipboard(formatJson(debugData?.raw))}
                style={styles.copyButton}
              >
                <Icon name="copy" size={12} color="#007AFF" />
              </TouchableOpacity>
            </View>
            <Text style={styles.jsonText}>
              {formatJson(debugData?.raw)}
            </Text>
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>完整JSON数据</Text>
              <TouchableOpacity 
                onPress={() => copyToClipboard(formatJson(rawData))}
                style={styles.copyButton}
              >
                <Icon name="copy" size={12} color="#007AFF" />
              </TouchableOpacity>
            </View>
            <Text style={styles.jsonText}>
              {formatJson(rawData)}
            </Text>
          </View>
        </ScrollView>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    width: 280,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#333',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 9999,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  dragHandle: {
    width: 4,
    height: 16,
    backgroundColor: '#666',
    borderRadius: 2,
    marginRight: 8,
  },
  title: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    flex: 1,
  },
  closeButton: {
    marginLeft: 8,
    padding: 4,
  },
  basicInfo: {
    padding: 12,
  },
  infoText: {
    color: '#fff',
    fontSize: 11,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  detailSection: {
    flex: 1,
    padding: 12,
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    color: '#007AFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  copyButton: {
    padding: 4,
  },
  jsonText: {
    color: '#00FF00',
    fontSize: 9,
    fontFamily: 'monospace',
    backgroundColor: '#111',
    padding: 8,
    borderRadius: 4,
    lineHeight: 12,
  },
});

export default DebugPanel;