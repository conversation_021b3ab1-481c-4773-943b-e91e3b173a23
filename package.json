{"name": "lingganggongjiao", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-native-camera-roll/camera-roll": "^7.8.3", "@react-native-community/netinfo": "^11.3.2", "buffer": "^6.0.3", "or": "^0.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "0.74.3", "react-native-alert-notification": "^0.4.0", "react-native-amap3d": "^3.2.4", "react-native-countdown-circle-timer": "^3.2.1", "react-native-fs": "^2.20.0", "react-native-linear-gradient": "^2.8.3", "react-native-md5": "^1.0.0", "react-native-modals": "^0.22.3", "react-native-safe-area-context": "^4.10.8", "react-native-svg": "^15.5.0", "react-native-swiper": "^1.6.0", "react-native-tcp-socket": "^6.2.0", "react-native-text-ticker": "^1.14.0", "react-native-vector-icons": "^10.1.0", "react-native-video": "^6.4.3", "react-native-view-shot": "^3.8.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@react-native/typescript-config": "0.74.85", "@types/react": "^18.2.6", "@types/react-native-modals": "^0.22.4", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "react-native": {"net": "react-native-tcp-socket"}, "engines": {"node": ">=18"}}