import TcpSocket from 'react-native-tcp-socket';
import { Buffer } from 'buffer';

const _options = {
  port: 7878,
  host: '*************',
  // localAddress: '127.0.0.1',
  reuseAddress: true,
  // interface: "wifi",
  // localPort: 20000,
};

let setTimeoutId: any;

// Create socket
export function startSocket (options: any, callBack: any) {
  if (!options.port || !options.host) return;
  options.reuseAddress = true;
  // console.log(options);
  let client = TcpSocket.connect(options, function() {
    console.log('connected');
  });
    
  client.on('data', function(data) {
    const _buf = Buffer.from(data);
    const _newData = _buf.slice(20, _buf.length - 1).toString();
    // console.log(_newData);
    // const cleanedString = _stringData.replace(/^[^{/\s]*/, '').replace(/[^}/\s]*$/, '');
    // const _cleanedString: any = cleanedString.replace("/", '');
    // const _newData = JSON.parse(cleanedString);
    // console.log(_cleanedString);
    // console.log(_newData);
    // client.destroy();
    // const _cleanedString = cleanedString.replace(/^[^{\s]*/, '').replace(/[^}\s]*$/, '');
    try {
      let _JSON = JSON.parse(_newData);
      callBack(_JSON);
      client.destroy();
    } catch (e) {
      // saving error
      console.log(e);
    }
  });
  
  client.on('error', function (error) {
    console.log(error);
  });
  
  client.on('close', function () {
    console.log('Connection closed!');
    if (setTimeoutId) {
      clearTimeout(setTimeoutId);
    }
    setTimeoutId = setTimeout(() => {
      startSocket(options, callBack);
    }, 1000);
  });

  return client;
}