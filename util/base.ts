// const baseUrl = 'https://dev.lightcloudapps.com/lingang-bus-system'
const baseUrl = 'https://wlgj.lingangdi.com:8003/apis'

// 最大重连次数
const maxRetries = 100

// 网络重连定时器管理
class NetworkRetryManager {
  private static retryTimers: Map<string, NodeJS.Timeout> = new Map();

  static setRetryTimer(key: string, callback: () => void, delay: number): void {
    this.clearRetryTimer(key);
    const timer = setTimeout(() => {
      callback();
      this.retryTimers.delete(key);
    }, delay);
    this.retryTimers.set(key, timer);
  }

  static clearRetryTimer(key: string): void {
    const timer = this.retryTimers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.retryTimers.delete(key);
    }
  }

  static clearAllRetryTimers(): void {
    this.retryTimers.forEach((timer) => {
      clearTimeout(timer);
    });
    this.retryTimers.clear();
  }
}

// 导出清除所有重连定时器的函数，供外部调用
export const clearAllNetworkRetryTimers = () => {
  NetworkRetryManager.clearAllRetryTimers();
};

/**
*
* GET请求
*
* @param url
* @param params {}包装
* @param headers
*
* @return {Promise} 返回一个Promise对象
*
* */
export const HttpBaseGet = (url: string, params?: any, headers?: any, retries?: number, delay = 3000) => {
  retries = retries == undefined ? maxRetries : retries;

  // 生成唯一的请求标识符
  const requestId = `${url}_${Date.now()}_${Math.random()}`;

  if (params) {
    const paramsArray: Array<string> = [];

    // 获取 params 内所有的 key
    let paramsKeyArray = Object.keys(params);
    // 通过 forEach 方法拿到数组中每个元素,将元素与参数的值进行拼接处理,并且放入 paramsArray 中
    paramsKeyArray.forEach(key => paramsArray.push(key + '=' + params[key]));

    // 网址拼接
    if (url.search(/\?/) === -1) {
      url += '?' + paramsArray.join('&');
    } else {
      url += paramsArray.join('&');
    }
  }

  // 向外部,返回一个Promise对象
  return new Promise(function (resolve, reject) {
    const checkNetworkAndRequest = () => {
      fetch(baseUrl + url, {
        method: 'GET',
        headers: headers
      })
        .then((response) => {
          // 网络恢复，清除重连定时器
          NetworkRetryManager.clearRetryTimer(requestId);
          return response.json();
        })
        .then((response) => {
          resolve(response);
        })
        .catch((error) => {
          console.log(error, 'GET请求网络错误');

          // 检查是否是网络错误
          const isNetworkError = error.name === 'TypeError' ||
                                error.message.includes('Network request failed') ||
                                error.message.includes('fetch');

          if (isNetworkError && retries > 0) {
            console.log(`GET请求网络错误，3秒后重连（剩余${retries}次重试）`);
            NetworkRetryManager.setRetryTimer(requestId, () => {
              HttpBaseGet(url.split('?')[0], params, headers, retries - 1, delay).then(resolve).catch(reject);
            }, delay);
          } else if (isNetworkError) {
            console.log('GET请求网络错误，重试次数已用完');
            NetworkRetryManager.clearRetryTimer(requestId);
            reject({ status: -1, error: 'Network error - max retries exceeded' });
          } else {
            // 非网络错误，直接拒绝
            NetworkRetryManager.clearRetryTimer(requestId);
            reject({ status: -1, error: error.message || 'Unknown error' });
          }
        });
    };

    checkNetworkAndRequest();
  });
}

/**
*
* POST请求
*
* @param url
* @param params {}包装
* @param headers
* @param retries -1 代表无线重连
* @return {Promise}
*
* */
export const HttpBasePost = (url: string, params: any, headers?: any, retries?: any, delay = 3000) => {
  retries = retries == undefined ? maxRetries : retries

  // 生成唯一的请求标识符
  const requestId = `${url}_${Date.now()}_${Math.random()}`;

  if (params) {
    // 初始化FormData
    var formData = new FormData();
    // 获取 params 内所有的 key
    let paramsKeyArray = Object.keys(params);
    // 通过 forEach 方法拿到数组中每个元素,将元素与参数的值进行拼接处理,并且放入 paramsArray 中
    paramsKeyArray.forEach(key => formData.append(key, params[key]));
  }

  return new Promise(function (resolve, reject) {
    // 检查网络连接状态
    const checkNetworkAndRequest = () => {
      fetch(baseUrl + url, {
        method: 'POST',
        headers: headers,
        body: formData,
      })
        .then((response) => {
          // 网络恢复，清除重连定时器
          NetworkRetryManager.clearRetryTimer(requestId);
          return response.json()
        })
        .then((response) => {
          resolve(response);
        })
        .catch((error) => {
          console.log(error, '网络请求错误');

          // 检查是否是网络错误
          const isNetworkError = error.name === 'TypeError' ||
                                error.message.includes('Network request failed') ||
                                error.message.includes('fetch');

          if (isNetworkError) {
            if (retries == -1) {
              console.log('网络错误，3秒后重连（无限重试）');
              NetworkRetryManager.setRetryTimer(requestId, () => {
                HttpBasePost(url, params, headers, -1, delay).then(resolve).catch(reject);
              }, delay);
            } else if (retries > 0) {
              console.log(`网络错误，3秒后重连（剩余${retries}次重试）`);
              NetworkRetryManager.setRetryTimer(requestId, () => {
                HttpBasePost(url, params, headers, retries - 1, delay).then(resolve).catch(reject);
              }, delay);
            } else {
              console.log('网络错误，重试次数已用完');
              NetworkRetryManager.clearRetryTimer(requestId);
              reject({ status: -1, error: 'Network error - max retries exceeded' });
            }
          } else {
            // 非网络错误，直接拒绝
            NetworkRetryManager.clearRetryTimer(requestId);
            reject({ status: -1, error: error.message || 'Unknown error' });
          }
        });
    };

    checkNetworkAndRequest();
  });
}

  // 定义一些常量
  var PI = 3.1415926535897932384626;
  var a = 6378245.0;
  var ee = 0.00669342162296594323;

/**
 * 判断是否在国内，不在国内则不做偏移
 * @param lng
 * @param lat
 * @returns {boolean}
 */
var out_of_china = function out_of_china(lng: number, lat: number) {
  var lat = +lat;
  var lng = +lng;
  // 纬度 3.86~53.55, 经度 73.66~135.05 
  return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
};

var transformlat = function transformlat(lng: number, lat: number) {
  var lat = +lat;
  var lng = +lng;
  var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0;
  ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0;
  return ret
};

var transformlng = function transformlng(lng: number, lat: number) {
  var lat = +lat;
  var lng = +lng;
  var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0;
  ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0;
  return ret
};

/**
   * WGS-84 转 GCJ-02
   * @param lng
   * @param lat
   * @returns {*[]}
   */
export const wgs84togcj02 = (lng: number, lat: number) => {
  var lat = +lat;
  var lng = +lng;
  if (out_of_china(lng, lat)) {
    return [lng, lat]
  } else {
    var dlat = transformlat(lng - 105.0, lat - 35.0);
    var dlng = transformlng(lng - 105.0, lat - 35.0);
    var radlat = lat / 180.0 * PI;
    var magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    var sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
    dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
    var mglat = lat + dlat;
    var mglng = lng + dlng;
    return [mglng, mglat]
  }
};