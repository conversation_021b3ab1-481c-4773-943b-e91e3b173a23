import { HttpBaseGet, HttpBasePost } from './base'
import { Alert } from 'react-native'
import MD5 from 'react-native-md5';

import { captureScreen } from "react-native-view-shot";

// const baseUrl = 'https://dev.lightcloudapps.com/lingang-bus-system'

// 获取当前时间汽车的站点信息列表
export const getListStation = (nbbm: string) => {
  // console.log(111)
  // const nbbm = 'W7G-088'
  // 获取当前时间

  const timestamp = Date.parse(new Date().toString())
  const token = 'a8bg1784hf8bd6'
  
  const query = `nbbm=${nbbm}&timestamp=${timestamp}&token=${token}`
  const sign = MD5.hex_md5(query)
  // console.log(query, sign)
  // console.log(query, sign)
  // return false
  const url = '/LingangBus/v1/station'
  const params = {
    nbbm,
    timestamp,
    sign
  }
  // console.log(params)
  return HttpBasePost(url, params, undefined, 0)
  // return HttpBasePost(url, params)

}

// 获取车到站点信息
export const getCurrentInfo = (linecode:any,stationCode:any,stationName:string,directions:any ) => {
  // console.log(111)
  // const nbbm = 'W7G-088'
  // 获取当前时间

  const timestamp = Date.parse(new Date().toString())
  
  const token = 'a8bg1784hf8bd6'
  
  // const stationName2 = unescape(encodeURIComponent(stationName))
  // let linecode2 = 12105
  // let stationCode2=124731
  const query = `linecode=${linecode}&stationCode=${stationCode}&stationName=${stationName}&directions=${directions}&timestamp=${timestamp}&token=${token}`
  const sign = MD5.hex_md5(unescape(encodeURIComponent(query)))

  // console.log(query, sign)
  const url = '/LingangBus/v1/positioning'
  const params = {
    linecode,
    stationCode,
    stationName,
    directions,
    timestamp,
    sign
  }
  return HttpBasePost(url, params)
}

// 获取APP状态
export const getAppState = () => {
  const url = '/LingangBus/v1/shield'
  return HttpBaseGet(url)
}

// 上报车辆状态
export const setCarStatus = (obg: any, time?: number) => {
  const _url = '/LingangBus/v1/car_device_log';
  const timestamp = Date.parse(new Date().toString());
  const token = 'a8bg1784hf8bd6';
  
  let params: any = {
    nbbm: obg.nbbm
  }

  // obg.linecode && (query = `${query}&linecode=${obg.linecode}`) && (params.linecode = obg.linecode);
  // obg.stationcode && (query = `${query}&stationcode=${obg.stationcode}`) && (params.stationcode = obg.stationcode);
  // obg.directions && (query = `${query}&directions=${obg.directions}`) && (params.directions = obg.directions);

  let query = `nbbm=${obg.nbbm}&linecode=${obg.linecode}&schedule_id=${obg.schedule_id}&directions=${obg.directions}&timestamp=${timestamp}&token=${token}`;
  const sign = MD5.hex_md5(unescape(encodeURIComponent(query)))
  params.sign = sign;
  params.timestamp = timestamp
  Object.assign(params, obg)

  // console.log(query, sign)
  captureScreen({
    format: "jpg",
    quality: 0.6,
    result: "data-uri"
  }).then(
    (uri) => {
      params.pic = uri;
      // console.log(uri)
      HttpBasePost(_url, params).then((res: any) => {
        if (res.code == 200) {
          console.log('截图上传成功')
          // setTimeout(() => {
          //   setCarStatus(obg, time);
          // }, time)
        }
      })
    },
    (error) => {
      HttpBasePost(_url, params).then((res: any) => {
        if (res.code == 200) {
          console.log('心跳调用成功')
          // setTimeout(() => {
          //   setCarStatus(obg, time);
          // }, time)
        }
      })
    }
  );
}