# Design Document

## Overview

设计一个轻量级的debug功能，在临港公交应用中实时显示从socket接收到的GPS坐标数据。该功能将集成到现有的App.tsx中，通过一个可切换的debug面板来显示坐标信息。

## Architecture

### 组件架构
```
App.tsx (主组件)
├── 现有功能组件
├── DebugPanel (新增debug面板组件)
└── DebugToggle (新增debug开关组件)
```

### 数据流
```
Socket Data → handleSocketData → Debug State → DebugPanel → UI Display
```

## Components and Interfaces

### 1. Debug State Management (App.tsx)

在App.tsx中添加以下状态：

```typescript
// Debug相关状态
const [debugMode, setDebugMode] = useState<boolean>(false);
const [debugCoordinates, setDebugCoordinates] = useState<{
  latitude: number | null;
  longitude: number | null;
  timestamp: string;
  raw: any;
}>({
  latitude: null,
  longitude: null,
  timestamp: '',
  raw: null
});
```

### 2. DebugPanel Component

创建一个新的debug面板组件：

```typescript
interface DebugPanelProps {
  visible: boolean;
  coordinates: {
    latitude: number | null;
    longitude: number | null;
    timestamp: string;
    raw: any;
  };
}

const DebugPanel: React.FC<DebugPanelProps> = ({ visible, coordinates }) => {
  // 组件实现
};
```

### 3. Socket Data Integration

修改现有的`handleSocketData`函数：

```typescript
const handleSocketData = useCallback((data: SocketCallBackData) => {
  // 现有逻辑保持不变
  if (data.position_3d) {
    // 现有位置处理逻辑...
    
    // 新增：更新debug坐标信息
    if (debugMode) {
      setDebugCoordinates({
        latitude: data.position_3d.latitude,
        longitude: data.position_3d.longitude,
        timestamp: new Date().toLocaleString(),
        raw: data.position_3d
      });
    }
  }
}, [timerManager, debugMode]);
```

## Data Models

### DebugCoordinates Interface

```typescript
interface DebugCoordinates {
  latitude: number | null;
  longitude: number | null;
  timestamp: string;
  raw: any; // 原始position_3d数据
}
```

## Error Handling

1. **坐标数据缺失**: 当position_3d数据不存在时，显示"无坐标数据"
2. **数据格式错误**: 当坐标数据格式不正确时，显示原始数据和错误提示
3. **性能保护**: 当debug模式关闭时，不执行debug相关的数据处理

## Testing Strategy

### 单元测试
- 测试debug状态的切换功能
- 测试坐标数据的提取和格式化
- 测试DebugPanel组件的渲染

### 集成测试
- 测试socket数据接收时debug信息的更新
- 测试debug模式对现有功能的影响
- 测试不同坐标数据格式的处理

### 用户测试
- 验证debug面板的可用性
- 确认debug信息的准确性
- 测试性能影响

## UI/UX Design

### Debug Toggle Button
- 位置：屏幕右上角
- 样式：半透明背景，小尺寸按钮
- 文本：DEBUG ON/OFF

### Debug Panel
- 位置：屏幕左上角，不遮挡主要功能
- 样式：半透明黑色背景，白色文字
- 内容：
  ```
  DEBUG INFO
  纬度: 30.89462
  经度: 121.906606
  时间: 2024-01-20 14:30:25
  原始: {...}
  ```

### 响应式设计
- 适配不同屏幕尺寸
- 确保不影响现有UI布局
- 支持横屏和竖屏模式