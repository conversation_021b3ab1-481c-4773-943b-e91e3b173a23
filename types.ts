// 定义API响应类型
export interface ApiResponse {
  code: number;
  message: string;
  data: any;
  config?: {
    shield_switch: string | boolean;
    shield_pic: string;
    shield_api_time: number;
    call_time?: number;
    bus_station_distance?: number;
    bus_api_logtime?: number;
  };
  schedule?: {
    id: string;
    lineName: string;
    qdzName: string;
    zdzName: string;
    startStationName: string;
    endStationName: string;
    price: string;
    FirstTime: string;
    EndTime: string;
    lineCode: string;
    fcsj: string;
    bcType: string;
    upDowm?: string;
    scheduleDateStr: string;
  };
  schedule_next?: {
    scheduleDateStr: string;
    fcsj: string;
  };
}

// 站点类型
export interface Station {
  stationName: string;
  stationCode: string;
  stationName2: string[];
  lat: number;
  lon: number;
  directions: any;
  other_line: OtherLine[];
  metro_arr: MetroStation[];
  station_data: MediaItem[];
}

// 媒体项类型
export interface MediaItem {
  type: 'video' | 'pic';
  name: string;
  material: {
    id: string | number;
    url: string;
  };
  fromUrl?: string;
  filepath?: string;
  key?: string;
  linecode?: string;
  trueUrl?: string;
  exists?: boolean;
  UrlHost?: any;
}

// 其他线路类型
export interface OtherLine {
  name: string;
  type?: 'bus' | 'metro';
  car?: {
    time: number;
  };
  linename?: string;
  time?: number;
}

// 地铁站类型
export interface MetroStation {
  name: string;
  type?: 'metro' | 'bus';
}

// 坐标点类型
export interface Point {
  lng: number;
  lat: number;
}

// 标记点类型
export interface Marker {
  title: string;
  lat: string;
  lon: string;
  pic: string;
}

// Socket回调数据类型
export interface SocketCallBackData {
  motion: {
    speed: string; // 时速
    heading: string; // 车头方向
  };
  position_3d: {
    latitude: number; //纬度，精确到小数点后 7 位(厘米级) 
    longitude: number; //经度，精确到小数点后 7 位(厘米级) 
    elevation: number; //海拔高度，单位是米，精确到小数点后两位
  };
  bsm: {
    warningInfo: number[];
  };
  waitTime: number;
  spat: {
    signalPhase: {
      direction: number;
      waitTime: number; // 红灯剩余时间
      ledStatus: number; // 车道信号灯状态（"5：绿"，"3：红 "，"4：黄"）
    }[];
    direction: number;
    spatAlarmType: number; // :1, 车速引导 2, 闯红灯告警 3、waittime 显示倒计时
  };
  rsm: {
    alarmData: {
      alarmType: number; // 1 为弱势交通参与者 2、为交叉口碰撞预警
    }[];
    vslInformation: {
      overspeedStatus: number; //超速状态(0:无预警，1：预警)
      advisorySpeed: number; //可变限速值 (km/h)
    };
  };
}

// Socket选项类型
export interface SocketOptions {
  host: string;
  port: number;
}

// Debug坐标数据类型
export interface DebugCoordinates {
  latitude: number | null;
  longitude: number | null;
  timestamp: string;
  raw: any; // 原始position_3d数据
}

// DebugPanel组件Props类型
export interface DebugPanelProps {
  visible: boolean;
  coordinates: DebugCoordinates;
}

// DebugToggle组件Props类型
export interface DebugToggleProps {
  debugMode: boolean;
  onToggle: () => void;
}